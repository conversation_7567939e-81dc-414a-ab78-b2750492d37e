package service

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"cmhk-platform/internal/shared/auth"
	"cmhk-platform/internal/shared/cache"
	"cmhk-platform/internal/shared/utils"
	"cmhk-platform/internal/user/model"
	"cmhk-platform/internal/user/repository"
	"cmhk-platform/pkg/logger"
)

// UserService defines the interface for user business logic
type UserService interface {
	CreateUser(ctx context.Context, req model.CreateUserRequest) (*model.User, error)
	GetUser(ctx context.Context, id uint64) (*model.User, error)
	UpdateUser(ctx context.Context, id uint64, req model.UpdateUserRequest) (*model.User, error)
	DeleteUser(ctx context.Context, id uint64) error
	ListUsers(ctx context.Context, query model.UserListQuery) ([]*model.User, int64, error)
	ChangePassword(ctx context.Context, userID uint64, req model.ChangePasswordRequest) error
	Login(ctx context.Context, req model.LoginRequest) (*model.LoginResponse, error)
	RefreshToken(ctx context.Context, req model.RefreshTokenRequest) (*model.LoginResponse, error)
	Logout(ctx context.Context, userID uint64, token string) error
	AssignRole(ctx context.Context, userID, roleID, grantedBy uint64) error
	RemoveRole(ctx context.Context, userID, roleID uint64) error
	LockUser(ctx context.Context, userID uint64) error
	UnlockUser(ctx context.Context, userID uint64) error
}

// userService implements UserService interface
type userService struct {
	userRepo    repository.UserRepository
	roleRepo    repository.RoleRepository
	jwtManager  *auth.JWTManager
	cache       *cache.CacheManager
	logger      *logger.Logger
}

// NewUserService creates a new user service
func NewUserService(
	userRepo repository.UserRepository,
	roleRepo repository.RoleRepository,
	jwtManager *auth.JWTManager,
	cache *cache.CacheManager,
	logger *logger.Logger,
) UserService {
	return &userService{
		userRepo:   userRepo,
		roleRepo:   roleRepo,
		jwtManager: jwtManager,
		cache:      cache,
		logger:     logger,
	}
}

// CreateUser creates a new user
func (s *userService) CreateUser(ctx context.Context, req model.CreateUserRequest) (*model.User, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// Check if user already exists
	if _, err := s.userRepo.GetByUsername(ctx, req.Username); err == nil {
		return nil, model.ErrUserAlreadyExists
	}

	if req.Email != "" {
		if _, err := s.userRepo.GetByEmail(ctx, req.Email); err == nil {
			return nil, model.ErrUserAlreadyExists
		}
	}

	if req.Phone != "" {
		if _, err := s.userRepo.GetByPhone(ctx, req.Phone); err == nil {
			return nil, model.ErrUserAlreadyExists
		}
	}

	// Validate password strength
	if err := utils.ValidatePasswordStrength(req.Password); err != nil {
		return nil, model.ErrPasswordTooWeak
	}

	// Generate salt and hash password
	salt, err := utils.GenerateSalt(16)
	if err != nil {
		return nil, fmt.Errorf("failed to generate salt: %w", err)
	}

	passwordHash, err := utils.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := &model.User{
		Username:     req.Username,
		Email:        req.Email,
		Phone:        req.Phone,
		PasswordHash: passwordHash,
		Salt:         salt,
		Status:       model.UserStatusActive,
		TenantID:     req.TenantID,
	}

	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	s.logger.Info("User created successfully",
		zap.Uint64("user_id", user.ID),
		zap.String("username", user.Username))

	return user, nil
}

// GetUser retrieves a user by ID
func (s *userService) GetUser(ctx context.Context, id uint64) (*model.User, error) {
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// UpdateUser updates a user
func (s *userService) UpdateUser(ctx context.Context, id uint64, req model.UpdateUserRequest) (*model.User, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// Get existing user
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Check if email is being changed and already exists
	if req.Email != "" && req.Email != user.Email {
		if _, err := s.userRepo.GetByEmail(ctx, req.Email); err == nil {
			return nil, model.ErrUserAlreadyExists
		}
		user.Email = req.Email
	}

	// Check if phone is being changed and already exists
	if req.Phone != "" && req.Phone != user.Phone {
		if _, err := s.userRepo.GetByPhone(ctx, req.Phone); err == nil {
			return nil, model.ErrUserAlreadyExists
		}
		user.Phone = req.Phone
	}

	// Update status if provided
	if req.Status >= 0 {
		user.Status = req.Status
	}

	// Save changes
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	s.logger.Info("User updated successfully",
		zap.Uint64("user_id", user.ID),
		zap.String("username", user.Username))

	return user, nil
}

// DeleteUser soft deletes a user
func (s *userService) DeleteUser(ctx context.Context, id uint64) error {
	// Check if user exists
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// Delete user
	if err := s.userRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	s.logger.Info("User deleted successfully",
		zap.Uint64("user_id", user.ID),
		zap.String("username", user.Username))

	return nil
}

// ListUsers retrieves users with pagination and filtering
func (s *userService) ListUsers(ctx context.Context, query model.UserListQuery) ([]*model.User, int64, error) {
	return s.userRepo.List(ctx, query)
}

// ChangePassword changes a user's password
func (s *userService) ChangePassword(ctx context.Context, userID uint64, req model.ChangePasswordRequest) error {
	// Validate request
	if err := req.Validate(); err != nil {
		return err
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// Verify current password
	if !utils.CheckPasswordHash(req.CurrentPassword, user.PasswordHash) {
		return model.ErrInvalidCredentials
	}

	// Validate new password strength
	if err := utils.ValidatePasswordStrength(req.NewPassword); err != nil {
		return model.ErrPasswordTooWeak
	}

	// Hash new password
	newPasswordHash, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	// Update password
	user.PasswordHash = newPasswordHash
	if err := s.userRepo.Update(ctx, user); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	s.logger.Info("Password changed successfully",
		zap.Uint64("user_id", userID))

	return nil
}

// Login authenticates a user and returns tokens
func (s *userService) Login(ctx context.Context, req model.LoginRequest) (*model.LoginResponse, error) {
	// Get user by username
	user, err := s.userRepo.GetByUsername(ctx, req.Username)
	if err != nil {
		if err == model.ErrUserNotFound {
			return nil, model.ErrInvalidCredentials
		}
		return nil, err
	}

	// Check user status
	if !user.IsActive() {
		if user.Status == model.UserStatusLocked {
			return nil, model.ErrUserLocked
		}
		return nil, model.ErrUserInactive
	}

	// Check if user is temporarily locked
	if user.IsLocked() {
		return nil, model.ErrUserLocked
	}

	// Verify password
	if !utils.CheckPasswordHash(req.Password, user.PasswordHash) {
		// Increment failed login count
		if err := s.userRepo.IncrementFailedLoginCount(ctx, user.ID); err != nil {
			s.logger.Error("Failed to increment failed login count", zap.Error(err))
		}

		// Lock user if too many failed attempts
		if user.FailedLoginCount >= 4 { // 5 attempts total
			if err := s.userRepo.LockUser(ctx, user.ID); err != nil {
				s.logger.Error("Failed to lock user", zap.Error(err))
			}
			return nil, model.ErrTooManyFailedLogins
		}

		return nil, model.ErrInvalidCredentials
	}

	// Reset failed login count on successful login
	if user.FailedLoginCount > 0 {
		if err := s.userRepo.ResetFailedLoginCount(ctx, user.ID); err != nil {
			s.logger.Error("Failed to reset failed login count", zap.Error(err))
		}
	}

	// Update last login time
	if err := s.userRepo.UpdateLastLogin(ctx, user.ID); err != nil {
		s.logger.Error("Failed to update last login time", zap.Error(err))
	}

	// Generate tokens
	accessToken, err := s.jwtManager.GenerateToken(
		user.ID,
		user.Username,
		user.TenantID,
		user.GetRoleCodes(),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := s.jwtManager.GenerateRefreshToken(user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Cache refresh token
	refreshTokenKey := cache.TokenCacheKey("refresh", refreshToken)
	if err := s.cache.SetWithTTL(ctx, refreshTokenKey, user.ID, s.jwtManager.GetRefreshExpiry()); err != nil {
		s.logger.Error("Failed to cache refresh token", zap.Error(err))
	}

	s.logger.Info("User logged in successfully",
		zap.Uint64("user_id", user.ID),
		zap.String("username", user.Username))

	return &model.LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    time.Now().Add(s.jwtManager.GetTokenExpiry()),
		User:         user.ToUserInfo(),
	}, nil
}

// RefreshToken refreshes an access token using a refresh token
func (s *userService) RefreshToken(ctx context.Context, req model.RefreshTokenRequest) (*model.LoginResponse, error) {
	// Validate refresh token
	userID, err := s.jwtManager.ValidateRefreshToken(req.RefreshToken)
	if err != nil {
		return nil, model.ErrRefreshTokenInvalid
	}

	// Check if refresh token exists in cache
	refreshTokenKey := cache.TokenCacheKey("refresh", req.RefreshToken)
	exists, err := s.cache.Exists(ctx, refreshTokenKey)
	if err != nil {
		s.logger.Error("Failed to check refresh token in cache", zap.Error(err))
	} else if !exists {
		return nil, model.ErrRefreshTokenInvalid
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Check user status
	if !user.IsActive() {
		return nil, model.ErrUserInactive
	}

	// Generate new tokens
	accessToken, err := s.jwtManager.GenerateToken(
		user.ID,
		user.Username,
		user.TenantID,
		user.GetRoleCodes(),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	newRefreshToken, err := s.jwtManager.GenerateRefreshToken(user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Remove old refresh token from cache
	if err := s.cache.Delete(ctx, refreshTokenKey); err != nil {
		s.logger.Error("Failed to remove old refresh token from cache", zap.Error(err))
	}

	// Cache new refresh token
	newRefreshTokenKey := cache.TokenCacheKey("refresh", newRefreshToken)
	if err := s.cache.SetWithTTL(ctx, newRefreshTokenKey, user.ID, s.jwtManager.GetRefreshExpiry()); err != nil {
		s.logger.Error("Failed to cache new refresh token", zap.Error(err))
	}

	s.logger.Info("Token refreshed successfully",
		zap.Uint64("user_id", user.ID))

	return &model.LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    time.Now().Add(s.jwtManager.GetTokenExpiry()),
		User:         user.ToUserInfo(),
	}, nil
}

// Logout logs out a user by invalidating tokens
func (s *userService) Logout(ctx context.Context, userID uint64, token string) error {
	// Remove refresh token from cache if provided
	if token != "" {
		refreshTokenKey := cache.TokenCacheKey("refresh", token)
		if err := s.cache.Delete(ctx, refreshTokenKey); err != nil {
			s.logger.Error("Failed to remove refresh token from cache", zap.Error(err))
		}
	}

	s.logger.Info("User logged out successfully",
		zap.Uint64("user_id", userID))

	return nil
}

// AssignRole assigns a role to a user
func (s *userService) AssignRole(ctx context.Context, userID, roleID, grantedBy uint64) error {
	// Check if user exists
	if _, err := s.userRepo.GetByID(ctx, userID); err != nil {
		return err
	}

	// Check if role exists
	if _, err := s.roleRepo.GetByID(ctx, roleID); err != nil {
		return err
	}

	// Assign role
	if err := s.userRepo.AssignRole(ctx, userID, roleID, grantedBy); err != nil {
		return fmt.Errorf("failed to assign role: %w", err)
	}

	s.logger.Info("Role assigned successfully",
		zap.Uint64("user_id", userID),
		zap.Uint64("role_id", roleID))

	return nil
}

// RemoveRole removes a role from a user
func (s *userService) RemoveRole(ctx context.Context, userID, roleID uint64) error {
	// Remove role
	if err := s.userRepo.RemoveRole(ctx, userID, roleID); err != nil {
		return fmt.Errorf("failed to remove role: %w", err)
	}

	s.logger.Info("Role removed successfully",
		zap.Uint64("user_id", userID),
		zap.Uint64("role_id", roleID))

	return nil
}

// LockUser locks a user account
func (s *userService) LockUser(ctx context.Context, userID uint64) error {
	if err := s.userRepo.LockUser(ctx, userID); err != nil {
		return fmt.Errorf("failed to lock user: %w", err)
	}

	s.logger.Info("User locked successfully",
		zap.Uint64("user_id", userID))

	return nil
}

// UnlockUser unlocks a user account
func (s *userService) UnlockUser(ctx context.Context, userID uint64) error {
	if err := s.userRepo.UnlockUser(ctx, userID); err != nil {
		return fmt.Errorf("failed to unlock user: %w", err)
	}

	s.logger.Info("User unlocked successfully",
		zap.Uint64("user_id", userID))

	return nil
}
