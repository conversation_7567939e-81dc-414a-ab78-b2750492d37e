# CMHK通信平台 - Golang单体应用项目结构

## 项目目录结构

```
cmhk-platform/
├── cmd/
│   └── server/
│       └── main.go                 # 应用程序入口点
├── internal/                       # 私有应用代码
│   ├── user/                       # 用户模块 (原user-service)
│   │   ├── handler/               # HTTP请求处理器
│   │   │   ├── user_handler.go
│   │   │   ├── auth_handler.go
│   │   │   └── role_handler.go
│   │   ├── service/               # 业务逻辑层
│   │   │   ├── user_service.go
│   │   │   ├── auth_service.go
│   │   │   └── role_service.go
│   │   ├── repository/            # 数据访问层
│   │   │   ├── user_repository.go
│   │   │   └── role_repository.go
│   │   └── model/                 # 数据模型
│   │       ├── user.go
│   │       ├── role.go
│   │       └── dto.go
│   ├── communication/             # 通信模块 (原communication-service)
│   │   ├── handler/
│   │   │   ├── message_handler.go
│   │   │   └── template_handler.go
│   │   ├── service/
│   │   │   ├── message_service.go
│   │   │   ├── channel_service.go
│   │   │   └── template_service.go
│   │   ├── repository/
│   │   │   ├── message_repository.go
│   │   │   └── template_repository.go
│   │   ├── model/
│   │   │   ├── message.go
│   │   │   └── template.go
│   │   └── channels/              # 渠道处理器
│   │       ├── sms_handler.go
│   │       ├── whatsapp_handler.go
│   │       └── wechat_handler.go
│   ├── customer/                  # 客户模块 (原customer-service)
│   │   ├── handler/
│   │   │   ├── customer_handler.go
│   │   │   └── segment_handler.go
│   │   ├── service/
│   │   │   ├── customer_service.go
│   │   │   ├── profile_service.go
│   │   │   └── segment_service.go
│   │   ├── repository/
│   │   │   ├── customer_repository.go
│   │   │   └── segment_repository.go
│   │   └── model/
│   │       ├── customer.go
│   │       ├── profile.go
│   │       └── segment.go
│   ├── marketing/                 # 营销模块 (原marketing-service)
│   │   ├── handler/
│   │   │   ├── campaign_handler.go
│   │   │   └── shortlink_handler.go
│   │   ├── service/
│   │   │   ├── campaign_service.go
│   │   │   └── shortlink_service.go
│   │   ├── repository/
│   │   │   ├── campaign_repository.go
│   │   │   └── shortlink_repository.go
│   │   └── model/
│   │       ├── campaign.go
│   │       └── shortlink.go
│   ├── analytics/                 # 分析模块 (原analytics-service)
│   │   ├── handler/
│   │   │   ├── metrics_handler.go
│   │   │   └── report_handler.go
│   │   ├── service/
│   │   │   ├── analytics_service.go
│   │   │   └── report_service.go
│   │   ├── repository/
│   │   │   └── analytics_repository.go
│   │   └── model/
│   │       ├── metrics.go
│   │       └── report.go
│   ├── platform/                  # 平台模块 (原platform-service)
│   │   ├── handler/
│   │   │   ├── config_handler.go
│   │   │   ├── audit_handler.go
│   │   │   └── health_handler.go
│   │   ├── service/
│   │   │   ├── config_service.go
│   │   │   ├── audit_service.go
│   │   │   └── notification_service.go
│   │   ├── repository/
│   │   │   ├── config_repository.go
│   │   │   └── audit_repository.go
│   │   └── model/
│   │       ├── config.go
│   │       ├── audit.go
│   │       └── notification.go
│   ├── file/                      # 文件模块 (原file-service)
│   │   ├── handler/
│   │   │   └── file_handler.go
│   │   ├── service/
│   │   │   └── file_service.go
│   │   ├── repository/
│   │   │   └── file_repository.go
│   │   └── model/
│   │       └── file.go
│   ├── workflow/                  # 工作流模块 (原workflow-service)
│   │   ├── handler/
│   │   │   └── workflow_handler.go
│   │   ├── service/
│   │   │   ├── workflow_service.go
│   │   │   └── engine_service.go
│   │   ├── repository/
│   │   │   └── workflow_repository.go
│   │   └── model/
│   │       ├── workflow.go
│   │       └── task.go
│   ├── shared/                    # 共享组件
│   │   ├── config/               # 配置管理
│   │   │   ├── config.go
│   │   │   └── database.go
│   │   ├── database/             # 数据库连接
│   │   │   ├── mysql.go
│   │   │   ├── redis.go
│   │   │   └── clickhouse.go
│   │   ├── cache/                # 缓存管理
│   │   │   ├── cache.go
│   │   │   └── redis_cache.go
│   │   ├── queue/                # 消息队列
│   │   │   ├── kafka.go
│   │   │   └── event_bus.go
│   │   ├── auth/                 # 认证授权
│   │   │   ├── jwt.go
│   │   │   ├── middleware.go
│   │   │   └── permission.go
│   │   ├── middleware/           # HTTP中间件
│   │   │   ├── cors.go
│   │   │   ├── logging.go
│   │   │   ├── metrics.go
│   │   │   └── recovery.go
│   │   ├── utils/                # 工具函数
│   │   │   ├── crypto.go
│   │   │   ├── validator.go
│   │   │   └── converter.go
│   │   └── types/                # 通用类型
│   │       ├── response.go
│   │       ├── pagination.go
│   │       └── errors.go
│   └── api/                       # API路由和网关
│       ├── router.go             # 路由配置
│       ├── middleware.go         # 全局中间件
│       └── docs.go               # API文档
├── pkg/                           # 可导出的公共库
│   ├── logger/                   # 日志库
│   │   └── logger.go
│   ├── metrics/                  # 监控指标
│   │   └── prometheus.go
│   └── validator/                # 验证器
│       └── validator.go
├── configs/                       # 配置文件
│   ├── config.yaml              # 默认配置
│   ├── config-dev.yaml          # 开发环境配置
│   ├── config-prod.yaml         # 生产环境配置
│   └── database/                # 数据库配置
│       ├── mysql.yaml
│       ├── redis.yaml
│       └── clickhouse.yaml
├── scripts/                       # 脚本文件
│   ├── build.sh                 # 构建脚本
│   ├── deploy.sh                # 部署脚本
│   ├── migrate.sh               # 数据库迁移脚本
│   └── test.sh                  # 测试脚本
├── docs/                          # 文档
│   ├── api/                     # API文档
│   ├── architecture/            # 架构文档
│   └── deployment/              # 部署文档
├── deployments/                   # 部署配置
│   ├── docker/
│   │   ├── Dockerfile
│   │   └── docker-compose.yml
│   ├── kubernetes/
│   │   ├── deployment.yaml
│   │   ├── service.yaml
│   │   └── configmap.yaml
│   └── helm/
│       ├── Chart.yaml
│       ├── values.yaml
│       └── templates/
├── tests/                         # 测试文件
│   ├── integration/             # 集成测试
│   ├── unit/                    # 单元测试
│   └── fixtures/                # 测试数据
├── .gitignore
├── .golangci.yml                 # Go代码检查配置
├── Makefile                      # 构建配置
├── README.md
├── go.mod                        # Go模块定义
└── go.sum                        # Go模块校验
```

## 模块设计原则

### 1. 领域驱动设计 (DDD)
- 每个模块代表一个业务领域
- 模块内部高内聚，模块间低耦合
- 通过接口定义模块边界

### 2. 分层架构
- **Handler层**: 处理HTTP请求，参数验证，响应格式化
- **Service层**: 业务逻辑处理，事务管理，模块间协调
- **Repository层**: 数据访问抽象，数据库操作封装
- **Model层**: 数据模型定义，业务实体表示

### 3. 依赖方向
- Handler → Service → Repository → Model
- 上层依赖下层，下层不依赖上层
- 通过接口实现依赖倒置

### 4. 共享组件
- 基础设施组件放在shared包中
- 可复用的工具函数放在utils包中
- 通用类型定义放在types包中

这种结构既保持了原有微服务的业务边界，又实现了单体应用的简化部署和管理。
