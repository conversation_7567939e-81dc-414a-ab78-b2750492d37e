package model

import "time"

// CreateCampaignRequest represents a request to create a new campaign
type CreateCampaignRequest struct {
	CampaignCode    string                 `json:"campaign_code" binding:"required,min=2,max=50"`
	CampaignName    string                 `json:"campaign_name" binding:"required,min=2,max=100"`
	Description     string                 `json:"description"`
	CampaignType    string                 `json:"campaign_type" binding:"required"`
	Channel         string                 `json:"channel" binding:"required"`
	StartTime       *time.Time             `json:"start_time"`
	EndTime         *time.Time             `json:"end_time"`
	TargetAudience  map[string]interface{} `json:"target_audience"`
	MessageTemplate string                 `json:"message_template" binding:"required"`
	Settings        map[string]interface{} `json:"settings"`
	Budget          float64                `json:"budget"`
	TenantID        uint64                 `json:"tenant_id" binding:"required"`
}

// UpdateCampaignRequest represents a request to update a campaign
type UpdateCampaignRequest struct {
	CampaignName    string                 `json:"campaign_name" binding:"omitempty,min=2,max=100"`
	Description     string                 `json:"description"`
	StartTime       *time.Time             `json:"start_time"`
	EndTime         *time.Time             `json:"end_time"`
	TargetAudience  map[string]interface{} `json:"target_audience"`
	MessageTemplate string                 `json:"message_template"`
	Settings        map[string]interface{} `json:"settings"`
	Budget          float64                `json:"budget"`
}

// CampaignResponse represents a campaign response
type CampaignResponse struct {
	ID              uint64                 `json:"id"`
	CampaignCode    string                 `json:"campaign_code"`
	CampaignName    string                 `json:"campaign_name"`
	Description     string                 `json:"description"`
	CampaignType    string                 `json:"campaign_type"`
	Channel         string                 `json:"channel"`
	Status          int8                   `json:"status"`
	StartTime       *time.Time             `json:"start_time"`
	EndTime         *time.Time             `json:"end_time"`
	TargetAudience  map[string]interface{} `json:"target_audience"`
	MessageTemplate string                 `json:"message_template"`
	Settings        map[string]interface{} `json:"settings"`
	Budget          float64                `json:"budget"`
	CreatedBy       uint64                 `json:"created_by"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// CampaignListQuery represents query parameters for listing campaigns
type CampaignListQuery struct {
	Page         int       `form:"page,default=1" binding:"min=1"`
	PageSize     int       `form:"page_size,default=20" binding:"min=1,max=100"`
	Status       *int8     `form:"status" binding:"omitempty,oneof=0 1 2 3 4 5"`
	CampaignType string    `form:"campaign_type"`
	Channel      string    `form:"channel"`
	Search       string    `form:"search"`
	DateFrom     time.Time `form:"date_from" time_format:"2006-01-02"`
	DateTo       time.Time `form:"date_to" time_format:"2006-01-02"`
	TenantID     uint64    `form:"tenant_id"`
}

// ExecuteCampaignRequest represents a request to execute a campaign
type ExecuteCampaignRequest struct {
	ExecutionType string                 `json:"execution_type" binding:"required"`
	ScheduledTime *time.Time             `json:"scheduled_time"`
	Settings      map[string]interface{} `json:"settings"`
}

// CampaignExecutionResponse represents a campaign execution response
type CampaignExecutionResponse struct {
	ID             uint64                 `json:"id"`
	CampaignID     uint64                 `json:"campaign_id"`
	ExecutionType  string                 `json:"execution_type"`
	Status         string                 `json:"status"`
	TargetCount    int64                  `json:"target_count"`
	SentCount      int64                  `json:"sent_count"`
	DeliveredCount int64                  `json:"delivered_count"`
	FailedCount    int64                  `json:"failed_count"`
	SuccessRate    float64                `json:"success_rate"`
	StartedAt      *time.Time             `json:"started_at"`
	CompletedAt    *time.Time             `json:"completed_at"`
	ErrorMessage   string                 `json:"error_message"`
	Metadata       map[string]interface{} `json:"metadata"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
}

// CampaignStatsResponse represents campaign statistics
type CampaignStatsResponse struct {
	TotalCampaigns    int64            `json:"total_campaigns"`
	ActiveCampaigns   int64            `json:"active_campaigns"`
	CompletedCampaigns int64           `json:"completed_campaigns"`
	StatusStats       map[string]int64 `json:"status_stats"`
	TypeStats         map[string]int64 `json:"type_stats"`
	ChannelStats      map[string]int64 `json:"channel_stats"`
}

// ToCampaignResponse converts Campaign model to CampaignResponse DTO
func (c *Campaign) ToCampaignResponse() CampaignResponse {
	var targetAudience map[string]interface{}
	var settings map[string]interface{}
	
	// Parse JSON fields (simplified for demo)
	targetAudience = make(map[string]interface{})
	settings = make(map[string]interface{})

	return CampaignResponse{
		ID:              c.ID,
		CampaignCode:    c.CampaignCode,
		CampaignName:    c.CampaignName,
		Description:     c.Description,
		CampaignType:    c.CampaignType,
		Channel:         c.Channel,
		Status:          c.Status,
		StartTime:       c.StartTime,
		EndTime:         c.EndTime,
		TargetAudience:  targetAudience,
		MessageTemplate: c.MessageTemplate,
		Settings:        settings,
		Budget:          c.Budget,
		CreatedBy:       c.CreatedBy,
		CreatedAt:       c.CreatedAt,
		UpdatedAt:       c.UpdatedAt,
	}
}

// ToCampaignExecutionResponse converts CampaignExecution model to CampaignExecutionResponse DTO
func (e *CampaignExecution) ToCampaignExecutionResponse() CampaignExecutionResponse {
	var metadata map[string]interface{}
	// Parse JSON metadata (simplified for demo)
	metadata = make(map[string]interface{})

	return CampaignExecutionResponse{
		ID:             e.ID,
		CampaignID:     e.CampaignID,
		ExecutionType:  e.ExecutionType,
		Status:         e.Status,
		TargetCount:    e.TargetCount,
		SentCount:      e.SentCount,
		DeliveredCount: e.DeliveredCount,
		FailedCount:    e.FailedCount,
		SuccessRate:    e.GetSuccessRate(),
		StartedAt:      e.StartedAt,
		CompletedAt:    e.CompletedAt,
		ErrorMessage:   e.ErrorMessage,
		Metadata:       metadata,
		CreatedAt:      e.CreatedAt,
		UpdatedAt:      e.UpdatedAt,
	}
}

// Validate validates CreateCampaignRequest
func (r *CreateCampaignRequest) Validate() error {
	if !ValidateCampaignType(r.CampaignType) {
		return ErrInvalidCampaignType
	}
	
	// Validate channel using communication model
	// This would import from communication package
	// if !communication.ValidateChannel(r.Channel) {
	//     return ErrInvalidChannel
	// }
	
	if r.EndTime != nil && r.StartTime != nil && r.EndTime.Before(*r.StartTime) {
		return ErrInvalidTimeRange
	}
	
	return nil
}

// Validate validates ExecuteCampaignRequest
func (r *ExecuteCampaignRequest) Validate() error {
	if !ValidateExecutionType(r.ExecutionType) {
		return ErrInvalidExecutionType
	}
	
	if r.ExecutionType == ExecutionTypeScheduled && r.ScheduledTime == nil {
		return ErrScheduledTimeRequired
	}
	
	return nil
}
