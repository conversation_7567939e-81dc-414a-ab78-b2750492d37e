groups:
  - name: cmhk-platform.rules
    rules:
      # Service availability alerts
      - alert: ServiceDown
        expr: up{job="cmhk-platform"} == 0
        for: 1m
        labels:
          severity: critical
          service: cmhk-platform
        annotations:
          summary: "CMHK Platform service is down"
          description: "CMHK Platform service has been down for more than 1 minute on instance {{ $labels.instance }}"

      - alert: ServiceHighErrorRate
        expr: rate(http_requests_total{job="cmhk-platform",status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          service: cmhk-platform
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} on instance {{ $labels.instance }}"

      - alert: ServiceHighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="cmhk-platform"}[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High response latency detected"
          description: "95th percentile latency is {{ $value }}s on instance {{ $labels.instance }}"

      # Database alerts
      - alert: DatabaseConnectionsHigh
        expr: database_connections_active{job="cmhk-platform"} > 80
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High database connection usage"
          description: "Database connections are at {{ $value }} on instance {{ $labels.instance }}"

      - alert: DatabaseConnectionsExhausted
        expr: database_connections_active{job="cmhk-platform"} > 95
        for: 2m
        labels:
          severity: critical
          service: cmhk-platform
        annotations:
          summary: "Database connections nearly exhausted"
          description: "Database connections are at {{ $value }} on instance {{ $labels.instance }}"

      - alert: DatabaseSlowQueries
        expr: rate(database_slow_queries_total{job="cmhk-platform"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High rate of slow database queries"
          description: "Slow query rate is {{ $value }}/sec on instance {{ $labels.instance }}"

      # Memory and resource alerts
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes{job="cmhk-platform"} > 1073741824  # 1GB
        for: 10m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizeBytes }} on instance {{ $labels.instance }}"

      - alert: HighGoroutineCount
        expr: go_goroutines{job="cmhk-platform"} > 1000
        for: 10m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High goroutine count"
          description: "Goroutine count is {{ $value }} on instance {{ $labels.instance }}"

      # Message processing alerts
      - alert: MessageProcessingFailures
        expr: rate(messages_failed_total{job="cmhk-platform"}[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High message processing failure rate"
          description: "Message failure rate is {{ $value }}/sec for channel {{ $labels.channel }} on instance {{ $labels.instance }}"

      - alert: MessageQueueBacklog
        expr: message_queue_size{job="cmhk-platform"} > 1000
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "Message queue backlog detected"
          description: "Message queue size is {{ $value }} for channel {{ $labels.channel }} on instance {{ $labels.instance }}"

      # Cache alerts
      - alert: CacheLowHitRatio
        expr: (rate(cache_hits_total{job="cmhk-platform"}[5m]) / (rate(cache_hits_total{job="cmhk-platform"}[5m]) + rate(cache_misses_total{job="cmhk-platform"}[5m]))) < 0.8
        for: 10m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "Low cache hit ratio"
          description: "Cache hit ratio is {{ $value | humanizePercentage }} on instance {{ $labels.instance }}"

      - alert: CacheConnectionFailures
        expr: rate(cache_errors_total{job="cmhk-platform"}[5m]) > 0.01
        for: 5m
        labels:
          severity: critical
          service: cmhk-platform
        annotations:
          summary: "Cache connection failures detected"
          description: "Cache error rate is {{ $value }}/sec on instance {{ $labels.instance }}"

      # Security alerts
      - alert: HighRateLimitHits
        expr: rate(rate_limit_exceeded_total{job="cmhk-platform"}[5m]) > 1
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High rate limit violations"
          description: "Rate limit exceeded {{ $value }} times/sec on instance {{ $labels.instance }}"

      - alert: SecurityThreatsDetected
        expr: rate(security_threats_total{job="cmhk-platform"}[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
          service: cmhk-platform
        annotations:
          summary: "Security threats detected"
          description: "Security threats detected at {{ $value }}/sec of type {{ $labels.threat_type }} on instance {{ $labels.instance }}"

      # Business logic alerts
      - alert: UserLoginFailures
        expr: rate(user_login_failures_total{job="cmhk-platform"}[5m]) > 0.5
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High user login failure rate"
          description: "Login failure rate is {{ $value }}/sec on instance {{ $labels.instance }}"

      - alert: CampaignExecutionFailures
        expr: rate(campaign_execution_failures_total{job="cmhk-platform"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "Campaign execution failures detected"
          description: "Campaign execution failure rate is {{ $value }}/sec on instance {{ $labels.instance }}"

      # File system alerts
      - alert: DiskSpaceHigh
        expr: (node_filesystem_size_bytes{job="node-exporter"} - node_filesystem_avail_bytes{job="node-exporter"}) / node_filesystem_size_bytes{job="node-exporter"} > 0.8
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High disk usage"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.device }} at {{ $labels.instance }}"

      - alert: DiskSpaceCritical
        expr: (node_filesystem_size_bytes{job="node-exporter"} - node_filesystem_avail_bytes{job="node-exporter"}) / node_filesystem_size_bytes{job="node-exporter"} > 0.9
        for: 2m
        labels:
          severity: critical
          service: cmhk-platform
        annotations:
          summary: "Critical disk usage"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.device }} at {{ $labels.instance }}"

      # Network alerts
      - alert: HighNetworkErrors
        expr: rate(node_network_receive_errs_total{job="node-exporter"}[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: cmhk-platform
        annotations:
          summary: "High network error rate"
          description: "Network error rate is {{ $value }}/sec on interface {{ $labels.device }} at {{ $labels.instance }}"
