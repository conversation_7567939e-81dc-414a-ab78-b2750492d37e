package types

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// APIResponse represents a standard API response
type APIResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Error     *APIError   `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
	RequestID string      `json:"request_id,omitempty"`
}

// APIError represents an API error
type APIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	APIResponse
	Pagination *Pagination `json:"pagination,omitempty"`
}

// Pagination represents pagination information
type Pagination struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// NewPagination creates a new pagination object
func NewPagination(page, pageSize int, total int64) *Pagination {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	
	return &Pagination{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// SuccessResponse creates a successful API response
func SuccessResponse(c *gin.Context, data interface{}) {
	response := APIResponse{
		Success:   true,
		Message:   "Success",
		Data:      data,
		Timestamp: time.Now(),
		RequestID: GetRequestID(c),
	}
	
	c.JSON(http.StatusOK, response)
}

// SuccessResponseWithMessage creates a successful API response with custom message
func SuccessResponseWithMessage(c *gin.Context, message string, data interface{}) {
	response := APIResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
		RequestID: GetRequestID(c),
	}
	
	c.JSON(http.StatusOK, response)
}

// ErrorResponse creates an error API response
func ErrorResponse(c *gin.Context, statusCode int, code, message string) {
	response := APIResponse{
		Success:   false,
		Message:   "Error",
		Error: &APIError{
			Code:    code,
			Message: message,
		},
		Timestamp: time.Now(),
		RequestID: GetRequestID(c),
	}
	
	c.JSON(statusCode, response)
}

// ErrorResponseWithDetails creates an error API response with details
func ErrorResponseWithDetails(c *gin.Context, statusCode int, code, message, details string) {
	response := APIResponse{
		Success:   false,
		Message:   "Error",
		Error: &APIError{
			Code:    code,
			Message: message,
			Details: details,
		},
		Timestamp: time.Now(),
		RequestID: GetRequestID(c),
	}
	
	c.JSON(statusCode, response)
}

// PaginatedSuccessResponse creates a paginated successful API response
func PaginatedSuccessResponse(c *gin.Context, data interface{}, pagination *Pagination) {
	response := PaginatedResponse{
		APIResponse: APIResponse{
			Success:   true,
			Message:   "Success",
			Data:      data,
			Timestamp: time.Now(),
			RequestID: GetRequestID(c),
		},
		Pagination: pagination,
	}
	
	c.JSON(http.StatusOK, response)
}

// ValidationErrorResponse creates a validation error response
func ValidationErrorResponse(c *gin.Context, errors map[string]string) {
	response := APIResponse{
		Success:   false,
		Message:   "Validation Error",
		Error: &APIError{
			Code:    "VALIDATION_ERROR",
			Message: "Request validation failed",
		},
		Data:      errors,
		Timestamp: time.Now(),
		RequestID: GetRequestID(c),
	}
	
	c.JSON(http.StatusBadRequest, response)
}

// GetRequestID extracts request ID from context
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}

// Common error codes
const (
	ErrorCodeValidation     = "VALIDATION_ERROR"
	ErrorCodeUnauthorized   = "UNAUTHORIZED"
	ErrorCodeForbidden      = "FORBIDDEN"
	ErrorCodeNotFound       = "NOT_FOUND"
	ErrorCodeConflict       = "CONFLICT"
	ErrorCodeInternalError  = "INTERNAL_ERROR"
	ErrorCodeServiceError   = "SERVICE_ERROR"
	ErrorCodeDatabaseError  = "DATABASE_ERROR"
	ErrorCodeCacheError     = "CACHE_ERROR"
	ErrorCodeExternalError  = "EXTERNAL_ERROR"
)
