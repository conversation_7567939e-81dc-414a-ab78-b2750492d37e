# Golang API和服务层实现示例

## 1. API路由配置 (internal/api/router.go)

```go
package api

import (
    "github.com/gin-gonic/gin"
    "github.com/redis/go-redis/v9"
    "gorm.io/gorm"
    "go.uber.org/zap"

    "cmhk-platform/internal/shared/config"
    "cmhk-platform/internal/shared/middleware"
    "cmhk-platform/internal/user/handler"
    userRepo "cmhk-platform/internal/user/repository"
    userService "cmhk-platform/internal/user/service"
    commHandler "cmhk-platform/internal/communication/handler"
    commRepo "cmhk-platform/internal/communication/repository"
    commService "cmhk-platform/internal/communication/service"
)

type Dependencies struct {
    Logger     *zap.Logger
    Config     *config.Config
    MySQL      *gorm.DB
    Redis      *redis.ClusterClient
    ClickHouse *gorm.DB
}

func NewRouter(deps *Dependencies) *gin.Engine {
    router := gin.New()

    // 全局中间件
    router.Use(middleware.Logger(deps.Logger))
    router.Use(middleware.Recovery(deps.Logger))
    router.Use(middleware.CORS())
    router.Use(middleware.RequestID())
    router.Use(middleware.Metrics())

    // 健康检查
    router.GET("/health", func(c *gin.Context) {
        c.JSON(200, gin.H{"status": "ok"})
    })

    // API版本分组
    v1 := router.Group("/api/v1")
    {
        // 初始化用户模块
        userRepository := userRepo.NewUserRepository(deps.MySQL)
        roleRepository := userRepo.NewRoleRepository(deps.MySQL)
        userSvc := userService.NewUserService(userRepository, roleRepository, deps.Config, deps.Logger)
        userHandler := handler.NewUserHandler(userSvc, deps.Logger)

        // 用户相关路由
        users := v1.Group("/users")
        {
            users.POST("/register", userHandler.Register)
            users.POST("/login", userHandler.Login)
            users.POST("/refresh", userHandler.RefreshToken)
            
            // 需要认证的路由
            authenticated := users.Group("")
            authenticated.Use(middleware.JWTAuth(deps.Config.Auth.JWTSecret))
            {
                authenticated.GET("/:id", userHandler.GetUser)
                authenticated.PUT("/:id", userHandler.UpdateUser)
                authenticated.DELETE("/:id", userHandler.DeleteUser)
                authenticated.GET("", userHandler.ListUsers)
                authenticated.POST("/:id/roles", userHandler.AssignRole)
                authenticated.DELETE("/:id/roles/:roleId", userHandler.RemoveRole)
            }
        }

        // 角色管理路由
        roles := v1.Group("/roles")
        roles.Use(middleware.JWTAuth(deps.Config.Auth.JWTSecret))
        {
            roles.POST("", userHandler.CreateRole)
            roles.GET("/:id", userHandler.GetRole)
            roles.PUT("/:id", userHandler.UpdateRole)
            roles.DELETE("/:id", userHandler.DeleteRole)
            roles.GET("", userHandler.ListRoles)
        }

        // 初始化通信模块
        messageRepository := commRepo.NewMessageRepository(deps.MySQL)
        templateRepository := commRepo.NewTemplateRepository(deps.MySQL)
        commSvc := commService.NewCommunicationService(
            messageRepository, 
            templateRepository, 
            deps.Redis, 
            deps.Config, 
            deps.Logger,
        )
        commHandler := commHandler.NewMessageHandler(commSvc, deps.Logger)

        // 消息相关路由
        messages := v1.Group("/messages")
        messages.Use(middleware.JWTAuth(deps.Config.Auth.JWTSecret))
        {
            messages.POST("/send", commHandler.SendMessage)
            messages.POST("/batch", commHandler.SendBatchMessages)
            messages.GET("/:id", commHandler.GetMessageStatus)
            messages.GET("", commHandler.ListMessages)
        }

        // 模板管理路由
        templates := v1.Group("/templates")
        templates.Use(middleware.JWTAuth(deps.Config.Auth.JWTSecret))
        {
            templates.POST("", commHandler.CreateTemplate)
            templates.GET("/:id", commHandler.GetTemplate)
            templates.PUT("/:id", commHandler.UpdateTemplate)
            templates.DELETE("/:id", commHandler.DeleteTemplate)
            templates.GET("", commHandler.ListTemplates)
        }
    }

    return router
}
```

## 2. 用户服务实现 (internal/user/service/user_service.go)

```go
package service

import (
    "context"
    "crypto/rand"
    "crypto/sha256"
    "encoding/hex"
    "fmt"
    "time"

    "github.com/golang-jwt/jwt/v5"
    "go.uber.org/zap"
    "golang.org/x/crypto/bcrypt"

    "cmhk-platform/internal/shared/config"
    "cmhk-platform/internal/shared/types"
    "cmhk-platform/internal/user/model"
    "cmhk-platform/internal/user/repository"
)

type UserService interface {
    Register(ctx context.Context, req *model.CreateUserRequest) (*model.User, error)
    Login(ctx context.Context, req *model.LoginRequest) (*model.LoginResponse, error)
    RefreshToken(ctx context.Context, refreshToken string) (*model.LoginResponse, error)
    GetUser(ctx context.Context, id uint64) (*model.User, error)
    UpdateUser(ctx context.Context, id uint64, req *model.UpdateUserRequest) error
    DeleteUser(ctx context.Context, id uint64) error
    ListUsers(ctx context.Context, tenantID uint64, page, size int) (*types.PagedResponse[*model.User], error)
    AssignRole(ctx context.Context, userID, roleID uint64) error
    RemoveRole(ctx context.Context, userID, roleID uint64) error
}

type userService struct {
    userRepo repository.UserRepository
    roleRepo repository.RoleRepository
    config   *config.Config
    logger   *zap.Logger
}

func NewUserService(
    userRepo repository.UserRepository,
    roleRepo repository.RoleRepository,
    config *config.Config,
    logger *zap.Logger,
) UserService {
    return &userService{
        userRepo: userRepo,
        roleRepo: roleRepo,
        config:   config,
        logger:   logger,
    }
}

func (s *userService) Register(ctx context.Context, req *model.CreateUserRequest) (*model.User, error) {
    // 检查用户名是否已存在
    existingUser, err := s.userRepo.GetByUsername(ctx, req.Username)
    if err == nil && existingUser != nil {
        return nil, types.ErrUserAlreadyExists
    }

    // 检查邮箱是否已存在
    if req.Email != "" {
        existingUser, err = s.userRepo.GetByEmail(ctx, req.Email)
        if err == nil && existingUser != nil {
            return nil, types.ErrEmailAlreadyExists
        }
    }

    // 生成盐值
    salt, err := generateSalt()
    if err != nil {
        return nil, fmt.Errorf("failed to generate salt: %w", err)
    }

    // 加密密码
    passwordHash, err := hashPassword(req.Password, salt)
    if err != nil {
        return nil, fmt.Errorf("failed to hash password: %w", err)
    }

    // 创建用户
    user := &model.User{
        Username:     req.Username,
        Email:        req.Email,
        Phone:        req.Phone,
        PasswordHash: passwordHash,
        Salt:         salt,
        Status:       1,
        TenantID:     req.TenantID,
    }

    if err := s.userRepo.Create(ctx, user); err != nil {
        return nil, fmt.Errorf("failed to create user: %w", err)
    }

    s.logger.Info("User registered successfully", 
        zap.Uint64("user_id", user.ID),
        zap.String("username", user.Username),
    )

    return user, nil
}

func (s *userService) Login(ctx context.Context, req *model.LoginRequest) (*model.LoginResponse, error) {
    // 获取用户
    user, err := s.userRepo.GetByUsername(ctx, req.Username)
    if err != nil {
        return nil, types.ErrInvalidCredentials
    }

    // 检查用户状态
    if user.Status != 1 {
        return nil, types.ErrUserDisabled
    }

    // 检查是否被锁定
    if user.LockedUntil != nil && user.LockedUntil.After(time.Now()) {
        return nil, types.ErrUserLocked
    }

    // 验证密码
    if !verifyPassword(req.Password, user.Salt, user.PasswordHash) {
        // 增加失败次数
        user.FailedLoginCount++
        if user.FailedLoginCount >= 5 {
            lockUntil := time.Now().Add(30 * time.Minute)
            user.LockedUntil = &lockUntil
        }
        s.userRepo.Update(ctx, user)
        return nil, types.ErrInvalidCredentials
    }

    // 重置失败次数
    user.FailedLoginCount = 0
    user.LockedUntil = nil
    now := time.Now()
    user.LastLoginAt = &now
    s.userRepo.Update(ctx, user)

    // 获取用户角色
    roles, err := s.userRepo.GetUserRoles(ctx, user.ID)
    if err != nil {
        s.logger.Error("Failed to get user roles", zap.Error(err))
        roles = []*model.Role{} // 设置为空角色
    }

    // 生成JWT令牌
    accessToken, err := s.generateAccessToken(user, roles)
    if err != nil {
        return nil, fmt.Errorf("failed to generate access token: %w", err)
    }

    refreshToken, err := s.generateRefreshToken(user.ID)
    if err != nil {
        return nil, fmt.Errorf("failed to generate refresh token: %w", err)
    }

    // 构建用户信息
    roleNames := make([]string, len(roles))
    for i, role := range roles {
        roleNames[i] = role.RoleCode
    }

    userInfo := model.UserInfo{
        ID:       user.ID,
        Username: user.Username,
        Email:    user.Email,
        Phone:    user.Phone,
        Status:   user.Status,
        TenantID: user.TenantID,
        Roles:    roleNames,
    }

    response := &model.LoginResponse{
        AccessToken:  accessToken,
        RefreshToken: refreshToken,
        ExpiresAt:    time.Now().Add(time.Duration(s.config.Auth.TokenExpiry) * time.Second),
        User:         userInfo,
    }

    s.logger.Info("User logged in successfully",
        zap.Uint64("user_id", user.ID),
        zap.String("username", user.Username),
    )

    return response, nil
}

// 辅助函数
func generateSalt() (string, error) {
    bytes := make([]byte, 16)
    if _, err := rand.Read(bytes); err != nil {
        return "", err
    }
    return hex.EncodeToString(bytes), nil
}

func hashPassword(password, salt string) (string, error) {
    hash := sha256.Sum256([]byte(password + salt))
    hashedPassword, err := bcrypt.GenerateFromPassword(hash[:], bcrypt.DefaultCost)
    if err != nil {
        return "", err
    }
    return string(hashedPassword), nil
}

func verifyPassword(password, salt, hashedPassword string) bool {
    hash := sha256.Sum256([]byte(password + salt))
    err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), hash[:])
    return err == nil
}

func (s *userService) generateAccessToken(user *model.User, roles []*model.Role) (string, error) {
    roleNames := make([]string, len(roles))
    for i, role := range roles {
        roleNames[i] = role.RoleCode
    }

    claims := jwt.MapClaims{
        "user_id":   user.ID,
        "username":  user.Username,
        "tenant_id": user.TenantID,
        "roles":     roleNames,
        "exp":       time.Now().Add(time.Duration(s.config.Auth.TokenExpiry) * time.Second).Unix(),
        "iat":       time.Now().Unix(),
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(s.config.Auth.JWTSecret))
}

func (s *userService) generateRefreshToken(userID uint64) (string, error) {
    claims := jwt.MapClaims{
        "user_id": userID,
        "exp":     time.Now().Add(time.Duration(s.config.Auth.RefreshExpiry) * time.Second).Unix(),
        "iat":     time.Now().Unix(),
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(s.config.Auth.JWTSecret))
}
