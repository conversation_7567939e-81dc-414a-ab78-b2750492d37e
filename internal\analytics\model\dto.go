package model

import "time"

// CreateReportRequest represents a request to create a new report
type CreateReportRequest struct {
	ReportCode  string                 `json:"report_code" binding:"required,min=2,max=50"`
	ReportName  string                 `json:"report_name" binding:"required,min=2,max=100"`
	Description string                 `json:"description"`
	ReportType  string                 `json:"report_type" binding:"required"`
	DataSource  string                 `json:"data_source" binding:"required"`
	Query       string                 `json:"query" binding:"required"`
	Parameters  map[string]interface{} `json:"parameters"`
	Schedule    map[string]interface{} `json:"schedule"`
	Format      string                 `json:"format" binding:"omitempty,oneof=json csv xlsx pdf"`
	TenantID    uint64                 `json:"tenant_id" binding:"required"`
}

// UpdateReportRequest represents a request to update a report
type UpdateReportRequest struct {
	ReportName  string                 `json:"report_name" binding:"omitempty,min=2,max=100"`
	Description string                 `json:"description"`
	Query       string                 `json:"query"`
	Parameters  map[string]interface{} `json:"parameters"`
	Schedule    map[string]interface{} `json:"schedule"`
	Format      string                 `json:"format" binding:"omitempty,oneof=json csv xlsx pdf"`
	Status      *int8                  `json:"status" binding:"omitempty,oneof=0 1"`
}

// ExecuteReportRequest represents a request to execute a report
type ExecuteReportRequest struct {
	Parameters map[string]interface{} `json:"parameters"`
	Format     string                 `json:"format" binding:"omitempty,oneof=json csv xlsx pdf"`
}

// ReportResponse represents a report response
type ReportResponse struct {
	ID          uint64                 `json:"id"`
	ReportCode  string                 `json:"report_code"`
	ReportName  string                 `json:"report_name"`
	Description string                 `json:"description"`
	ReportType  string                 `json:"report_type"`
	DataSource  string                 `json:"data_source"`
	Query       string                 `json:"query"`
	Parameters  map[string]interface{} `json:"parameters"`
	Schedule    map[string]interface{} `json:"schedule"`
	Format      string                 `json:"format"`
	Status      int8                   `json:"status"`
	CreatedBy   uint64                 `json:"created_by"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// ReportExecutionResponse represents a report execution response
type ReportExecutionResponse struct {
	ID           uint64                 `json:"id"`
	ReportID     uint64                 `json:"report_id"`
	Status       string                 `json:"status"`
	StartedAt    *time.Time             `json:"started_at"`
	CompletedAt  *time.Time             `json:"completed_at"`
	Duration     int64                  `json:"duration"`
	ResultSize   int64                  `json:"result_size"`
	ResultPath   string                 `json:"result_path"`
	ErrorMessage string                 `json:"error_message"`
	Parameters   map[string]interface{} `json:"parameters"`
	CreatedAt    time.Time              `json:"created_at"`
}

// MetricRequest represents a request to record a metric
type MetricRequest struct {
	MetricName string                 `json:"metric_name" binding:"required"`
	MetricType string                 `json:"metric_type" binding:"required"`
	Value      float64                `json:"value" binding:"required"`
	Unit       string                 `json:"unit"`
	Tags       map[string]interface{} `json:"tags"`
	Timestamp  *time.Time             `json:"timestamp"`
	TenantID   uint64                 `json:"tenant_id" binding:"required"`
}

// MetricQueryRequest represents a request to query metrics
type MetricQueryRequest struct {
	MetricName string                 `form:"metric_name" binding:"required"`
	StartTime  time.Time              `form:"start_time" binding:"required" time_format:"2006-01-02T15:04:05Z"`
	EndTime    time.Time              `form:"end_time" binding:"required" time_format:"2006-01-02T15:04:05Z"`
	Aggregation string                `form:"aggregation" binding:"omitempty,oneof=avg sum min max count"`
	Interval   string                 `form:"interval" binding:"omitempty"`
	Tags       map[string]interface{} `form:"tags"`
	TenantID   uint64                 `form:"tenant_id"`
}

// MetricResponse represents a metric response
type MetricResponse struct {
	MetricName string                 `json:"metric_name"`
	MetricType string                 `json:"metric_type"`
	Value      float64                `json:"value"`
	Unit       string                 `json:"unit"`
	Tags       map[string]interface{} `json:"tags"`
	Timestamp  time.Time              `json:"timestamp"`
}

// CreateDashboardRequest represents a request to create a dashboard
type CreateDashboardRequest struct {
	DashboardCode string                 `json:"dashboard_code" binding:"required,min=2,max=50"`
	DashboardName string                 `json:"dashboard_name" binding:"required,min=2,max=100"`
	Description   string                 `json:"description"`
	Layout        map[string]interface{} `json:"layout" binding:"required"`
	Widgets       []map[string]interface{} `json:"widgets" binding:"required"`
	IsPublic      bool                   `json:"is_public"`
	TenantID      uint64                 `json:"tenant_id" binding:"required"`
}

// UpdateDashboardRequest represents a request to update a dashboard
type UpdateDashboardRequest struct {
	DashboardName string                   `json:"dashboard_name" binding:"omitempty,min=2,max=100"`
	Description   string                   `json:"description"`
	Layout        map[string]interface{}   `json:"layout"`
	Widgets       []map[string]interface{} `json:"widgets"`
	IsPublic      *bool                    `json:"is_public"`
	Status        *int8                    `json:"status" binding:"omitempty,oneof=0 1"`
}

// DashboardResponse represents a dashboard response
type DashboardResponse struct {
	ID            uint64                   `json:"id"`
	DashboardCode string                   `json:"dashboard_code"`
	DashboardName string                   `json:"dashboard_name"`
	Description   string                   `json:"description"`
	Layout        map[string]interface{}   `json:"layout"`
	Widgets       []map[string]interface{} `json:"widgets"`
	IsPublic      bool                     `json:"is_public"`
	Status        int8                     `json:"status"`
	CreatedBy     uint64                   `json:"created_by"`
	CreatedAt     time.Time                `json:"created_at"`
	UpdatedAt     time.Time                `json:"updated_at"`
}

// CreateAlertRequest represents a request to create an alert
type CreateAlertRequest struct {
	AlertName   string                 `json:"alert_name" binding:"required,min=2,max=100"`
	Description string                 `json:"description"`
	MetricName  string                 `json:"metric_name" binding:"required"`
	Condition   map[string]interface{} `json:"condition" binding:"required"`
	Threshold   float64                `json:"threshold" binding:"required"`
	Severity    string                 `json:"severity" binding:"required,oneof=low medium high critical"`
	Actions     []map[string]interface{} `json:"actions"`
	TenantID    uint64                 `json:"tenant_id" binding:"required"`
}

// UpdateAlertRequest represents a request to update an alert
type UpdateAlertRequest struct {
	AlertName   string                   `json:"alert_name" binding:"omitempty,min=2,max=100"`
	Description string                   `json:"description"`
	Condition   map[string]interface{}   `json:"condition"`
	Threshold   *float64                 `json:"threshold"`
	Severity    string                   `json:"severity" binding:"omitempty,oneof=low medium high critical"`
	Actions     []map[string]interface{} `json:"actions"`
	Status      *int8                    `json:"status" binding:"omitempty,oneof=0 1"`
}

// AlertResponse represents an alert response
type AlertResponse struct {
	ID            uint64                   `json:"id"`
	AlertName     string                   `json:"alert_name"`
	Description   string                   `json:"description"`
	MetricName    string                   `json:"metric_name"`
	Condition     map[string]interface{}   `json:"condition"`
	Threshold     float64                  `json:"threshold"`
	Severity      string                   `json:"severity"`
	Actions       []map[string]interface{} `json:"actions"`
	Status        int8                     `json:"status"`
	LastTriggered *time.Time               `json:"last_triggered"`
	CreatedBy     uint64                   `json:"created_by"`
	CreatedAt     time.Time                `json:"created_at"`
	UpdatedAt     time.Time                `json:"updated_at"`
}

// AnalyticsStatsResponse represents analytics statistics
type AnalyticsStatsResponse struct {
	TotalReports     int64 `json:"total_reports"`
	ActiveReports    int64 `json:"active_reports"`
	TotalExecutions  int64 `json:"total_executions"`
	SuccessfulExecutions int64 `json:"successful_executions"`
	TotalDashboards  int64 `json:"total_dashboards"`
	ActiveAlerts     int64 `json:"active_alerts"`
	TriggeredAlerts  int64 `json:"triggered_alerts"`
}

// ToReportResponse converts Report model to ReportResponse DTO
func (r *Report) ToReportResponse() ReportResponse {
	var parameters map[string]interface{}
	var schedule map[string]interface{}
	
	// Parse JSON fields (simplified for demo)
	parameters = make(map[string]interface{})
	schedule = make(map[string]interface{})

	return ReportResponse{
		ID:          r.ID,
		ReportCode:  r.ReportCode,
		ReportName:  r.ReportName,
		Description: r.Description,
		ReportType:  r.ReportType,
		DataSource:  r.DataSource,
		Query:       r.Query,
		Parameters:  parameters,
		Schedule:    schedule,
		Format:      r.Format,
		Status:      r.Status,
		CreatedBy:   r.CreatedBy,
		CreatedAt:   r.CreatedAt,
		UpdatedAt:   r.UpdatedAt,
	}
}

// ToReportExecutionResponse converts ReportExecution model to ReportExecutionResponse DTO
func (e *ReportExecution) ToReportExecutionResponse() ReportExecutionResponse {
	var parameters map[string]interface{}
	// Parse JSON parameters (simplified for demo)
	parameters = make(map[string]interface{})

	return ReportExecutionResponse{
		ID:           e.ID,
		ReportID:     e.ReportID,
		Status:       e.Status,
		StartedAt:    e.StartedAt,
		CompletedAt:  e.CompletedAt,
		Duration:     e.Duration,
		ResultSize:   e.ResultSize,
		ResultPath:   e.ResultPath,
		ErrorMessage: e.ErrorMessage,
		Parameters:   parameters,
		CreatedAt:    e.CreatedAt,
	}
}

// Validate validates CreateReportRequest
func (r *CreateReportRequest) Validate() error {
	if !ValidateReportType(r.ReportType) {
		return ErrInvalidReportType
	}
	
	if !ValidateDataSource(r.DataSource) {
		return ErrInvalidDataSource
	}
	
	return nil
}

// Validate validates MetricRequest
func (r *MetricRequest) Validate() error {
	if !ValidateMetricType(r.MetricType) {
		return ErrInvalidMetricType
	}
	
	return nil
}
