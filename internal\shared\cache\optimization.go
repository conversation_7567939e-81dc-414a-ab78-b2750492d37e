package cache

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"cmhk-platform/pkg/logger"
)

// OptimizedCacheManager provides advanced caching capabilities with optimization features
type OptimizedCacheManager struct {
	client  *redis.Client
	logger  *logger.Logger
	config  *OptimizationConfig
	metrics *CacheMetrics
	mu      sync.RWMutex
}

// OptimizationConfig holds cache optimization settings
type OptimizationConfig struct {
	// Connection settings
	PoolSize     int           `yaml:"pool_size" mapstructure:"pool_size"`
	MinIdleConns int           `yaml:"min_idle_conns" mapstructure:"min_idle_conns"`
	MaxRetries   int           `yaml:"max_retries" mapstructure:"max_retries"`
	DialTimeout  time.Duration `yaml:"dial_timeout" mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `yaml:"read_timeout" mapstructure:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout" mapstructure:"write_timeout"`

	// Cache strategies
	DefaultTTL       time.Duration `yaml:"default_ttl" mapstructure:"default_ttl"`
	EnablePipeline   bool          `yaml:"enable_pipeline" mapstructure:"enable_pipeline"`
	PipelineSize     int           `yaml:"pipeline_size" mapstructure:"pipeline_size"`
	EnableMetrics    bool          `yaml:"enable_metrics" mapstructure:"enable_metrics"`
	EnableCompression bool         `yaml:"enable_compression" mapstructure:"enable_compression"`

	// Memory optimization
	MaxMemoryPolicy string `yaml:"max_memory_policy" mapstructure:"max_memory_policy"`
	KeyEvictionLRU  bool   `yaml:"key_eviction_lru" mapstructure:"key_eviction_lru"`
}

// CacheMetrics holds detailed cache performance metrics
type CacheMetrics struct {
	Hits           int64     `json:"hits"`
	Misses         int64     `json:"misses"`
	Sets           int64     `json:"sets"`
	Deletes        int64     `json:"deletes"`
	Errors         int64     `json:"errors"`
	TotalOps       int64     `json:"total_ops"`
	HitRatio       float64   `json:"hit_ratio"`
	AvgLatency     float64   `json:"avg_latency_ms"`
	MaxLatency     float64   `json:"max_latency_ms"`
	LastResetTime  time.Time `json:"last_reset_time"`
	MemoryUsage    int64     `json:"memory_usage_bytes"`
	KeyCount       int64     `json:"key_count"`
	ExpiredKeys    int64     `json:"expired_keys"`
	EvictedKeys    int64     `json:"evicted_keys"`
}

// NewOptimizedCacheManager creates a new optimized cache manager
func NewOptimizedCacheManager(client *redis.Client, logger *logger.Logger, config *OptimizationConfig) *OptimizedCacheManager {
	if config == nil {
		config = DefaultOptimizationConfig()
	}

	return &OptimizedCacheManager{
		client: client,
		logger: logger,
		config: config,
		metrics: &CacheMetrics{
			LastResetTime: time.Now(),
		},
	}
}

// DefaultOptimizationConfig returns default optimization settings
func DefaultOptimizationConfig() *OptimizationConfig {
	return &OptimizationConfig{
		PoolSize:          10,
		MinIdleConns:      5,
		MaxRetries:        3,
		DialTimeout:       time.Second * 5,
		ReadTimeout:       time.Second * 3,
		WriteTimeout:      time.Second * 3,
		DefaultTTL:        time.Minute * 15,
		EnablePipeline:    true,
		PipelineSize:      100,
		EnableMetrics:     true,
		EnableCompression: false,
		MaxMemoryPolicy:   "allkeys-lru",
		KeyEvictionLRU:    true,
	}
}

// GetOrSet retrieves a value from cache, or sets it using the provided function
func (c *OptimizedCacheManager) GetOrSet(ctx context.Context, key string, dest interface{}, fetcher func() (interface{}, error), ttl time.Duration) error {
	start := time.Now()
	defer c.recordLatency(time.Since(start))

	// Try to get from cache first
	err := c.GetJSON(ctx, key, dest)
	if err == nil {
		c.recordHit()
		return nil
	}

	c.recordMiss()

	// Cache miss, fetch the value
	value, err := fetcher()
	if err != nil {
		c.recordError()
		return fmt.Errorf("failed to fetch value: %w", err)
	}

	// Set in cache for next time
	if err := c.SetJSON(ctx, key, value, ttl); err != nil {
		c.logger.Warn("Failed to set cache after fetch", zap.String("key", key), zap.Error(err))
	}

	// Copy the fetched value to dest
	return c.copyValue(value, dest)
}

// SetJSON stores a JSON value in cache with compression if enabled
func (c *OptimizedCacheManager) SetJSON(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	start := time.Now()
	defer c.recordLatency(time.Since(start))

	if ttl == 0 {
		ttl = c.config.DefaultTTL
	}

	data, err := c.marshalValue(value)
	if err != nil {
		c.recordError()
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	err = c.client.Set(ctx, key, data, ttl).Err()
	if err != nil {
		c.recordError()
		c.logger.Error("Failed to set cache", zap.String("key", key), zap.Error(err))
		return fmt.Errorf("failed to set cache: %w", err)
	}

	c.recordSet()
	return nil
}

// GetJSON retrieves and unmarshals a JSON value from cache
func (c *OptimizedCacheManager) GetJSON(ctx context.Context, key string, dest interface{}) error {
	start := time.Now()
	defer c.recordLatency(time.Since(start))

	data, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			c.recordMiss()
			return fmt.Errorf("cache miss for key: %s", key)
		}
		c.recordError()
		c.logger.Error("Failed to get cache", zap.String("key", key), zap.Error(err))
		return fmt.Errorf("failed to get cache: %w", err)
	}

	err = c.unmarshalValue(data, dest)
	if err != nil {
		c.recordError()
		return fmt.Errorf("failed to unmarshal value: %w", err)
	}

	c.recordHit()
	return nil
}

// SetMultiple sets multiple key-value pairs using pipeline for better performance
func (c *OptimizedCacheManager) SetMultiple(ctx context.Context, data map[string]interface{}, ttl time.Duration) error {
	if !c.config.EnablePipeline || len(data) < 2 {
		// Use regular set for small batches
		for key, value := range data {
			if err := c.SetJSON(ctx, key, value, ttl); err != nil {
				return err
			}
		}
		return nil
	}

	start := time.Now()
	defer c.recordLatency(time.Since(start))

	pipe := c.client.Pipeline()
	
	for key, value := range data {
		marshaledData, err := c.marshalValue(value)
		if err != nil {
			c.recordError()
			return fmt.Errorf("failed to marshal value for key %s: %w", key, err)
		}
		pipe.Set(ctx, key, marshaledData, ttl)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		c.recordError()
		return fmt.Errorf("failed to execute pipeline: %w", err)
	}

	c.mu.Lock()
	c.metrics.Sets += int64(len(data))
	c.mu.Unlock()

	return nil
}

// DeletePattern deletes all keys matching a pattern using pipeline
func (c *OptimizedCacheManager) DeletePattern(ctx context.Context, pattern string) error {
	start := time.Now()
	defer c.recordLatency(time.Since(start))

	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		c.recordError()
		return fmt.Errorf("failed to get keys for pattern %s: %w", pattern, err)
	}

	if len(keys) == 0 {
		return nil
	}

	// Use pipeline for better performance when deleting many keys
	if c.config.EnablePipeline && len(keys) > 10 {
		pipe := c.client.Pipeline()
		for _, key := range keys {
			pipe.Del(ctx, key)
		}
		_, err = pipe.Exec(ctx)
	} else {
		err = c.client.Del(ctx, keys...).Err()
	}

	if err != nil {
		c.recordError()
		return fmt.Errorf("failed to delete keys: %w", err)
	}

	c.mu.Lock()
	c.metrics.Deletes += int64(len(keys))
	c.mu.Unlock()

	return nil
}

// GetMetrics returns current cache metrics
func (c *OptimizedCacheManager) GetMetrics() *CacheMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// Calculate hit ratio
	if c.metrics.Hits+c.metrics.Misses > 0 {
		c.metrics.HitRatio = float64(c.metrics.Hits) / float64(c.metrics.Hits+c.metrics.Misses) * 100
	}

	// Get Redis memory info
	info, err := c.client.Info(context.Background(), "memory").Result()
	if err == nil {
		// Parse memory usage from Redis INFO (simplified)
		c.metrics.MemoryUsage = c.parseMemoryUsage(info)
	}

	return &CacheMetrics{
		Hits:          c.metrics.Hits,
		Misses:        c.metrics.Misses,
		Sets:          c.metrics.Sets,
		Deletes:       c.metrics.Deletes,
		Errors:        c.metrics.Errors,
		TotalOps:      c.metrics.TotalOps,
		HitRatio:      c.metrics.HitRatio,
		AvgLatency:    c.metrics.AvgLatency,
		MaxLatency:    c.metrics.MaxLatency,
		LastResetTime: c.metrics.LastResetTime,
		MemoryUsage:   c.metrics.MemoryUsage,
		KeyCount:      c.metrics.KeyCount,
		ExpiredKeys:   c.metrics.ExpiredKeys,
		EvictedKeys:   c.metrics.EvictedKeys,
	}
}

// ResetMetrics resets all metrics counters
func (c *OptimizedCacheManager) ResetMetrics() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.metrics = &CacheMetrics{
		LastResetTime: time.Now(),
	}
}

// Helper methods for metrics recording
func (c *OptimizedCacheManager) recordHit() {
	if !c.config.EnableMetrics {
		return
	}
	c.mu.Lock()
	c.metrics.Hits++
	c.metrics.TotalOps++
	c.mu.Unlock()
}

func (c *OptimizedCacheManager) recordMiss() {
	if !c.config.EnableMetrics {
		return
	}
	c.mu.Lock()
	c.metrics.Misses++
	c.metrics.TotalOps++
	c.mu.Unlock()
}

func (c *OptimizedCacheManager) recordSet() {
	if !c.config.EnableMetrics {
		return
	}
	c.mu.Lock()
	c.metrics.Sets++
	c.metrics.TotalOps++
	c.mu.Unlock()
}

func (c *OptimizedCacheManager) recordError() {
	if !c.config.EnableMetrics {
		return
	}
	c.mu.Lock()
	c.metrics.Errors++
	c.metrics.TotalOps++
	c.mu.Unlock()
}

func (c *OptimizedCacheManager) recordLatency(duration time.Duration) {
	if !c.config.EnableMetrics {
		return
	}
	
	latencyMs := float64(duration.Nanoseconds()) / 1e6
	
	c.mu.Lock()
	if latencyMs > c.metrics.MaxLatency {
		c.metrics.MaxLatency = latencyMs
	}
	
	// Simple moving average for average latency
	if c.metrics.TotalOps > 0 {
		c.metrics.AvgLatency = (c.metrics.AvgLatency*float64(c.metrics.TotalOps-1) + latencyMs) / float64(c.metrics.TotalOps)
	} else {
		c.metrics.AvgLatency = latencyMs
	}
	c.mu.Unlock()
}

// Helper methods for value marshaling/unmarshaling
func (c *OptimizedCacheManager) marshalValue(value interface{}) (string, error) {
	// In production, you might want to implement compression here
	return MarshalJSON(value)
}

func (c *OptimizedCacheManager) unmarshalValue(data string, dest interface{}) error {
	// In production, you might want to implement decompression here
	return UnmarshalJSON(data, dest)
}

func (c *OptimizedCacheManager) copyValue(src, dest interface{}) error {
	// Simple implementation - marshal and unmarshal
	data, err := c.marshalValue(src)
	if err != nil {
		return err
	}
	return c.unmarshalValue(data, dest)
}

func (c *OptimizedCacheManager) parseMemoryUsage(info string) int64 {
	// Simplified parser - in production, you would parse the actual Redis INFO output
	return 0
}
