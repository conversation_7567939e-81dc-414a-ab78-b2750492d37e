#!/bin/bash

# CMHK Communication Platform Deployment Script

set -e

# Configuration
APP_NAME="cmhk-platform"
BUILD_DIR="bin"
DEPLOY_DIR="/opt/cmhk-platform"
SERVICE_NAME="cmhk-platform"
CONFIG_ENV=${1:-"production"}

echo "Starting deployment of CMHK Communication Platform..."
echo "Environment: $CONFIG_ENV"

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "This script should not be run as root for security reasons"
   exit 1
fi

# Create deployment directory
sudo mkdir -p $DEPLOY_DIR
sudo chown $USER:$USER $DEPLOY_DIR

# Build the application
echo "Building application..."
./scripts/build.sh

# Stop existing service if running
echo "Stopping existing service..."
sudo systemctl stop $SERVICE_NAME || true

# Backup current deployment
if [ -f "$DEPLOY_DIR/$APP_NAME" ]; then
    echo "Backing up current deployment..."
    sudo cp "$DEPLOY_DIR/$APP_NAME" "$DEPLOY_DIR/${APP_NAME}.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Copy new binary
echo "Deploying new binary..."
sudo cp "$BUILD_DIR/$APP_NAME" "$DEPLOY_DIR/"
sudo chmod +x "$DEPLOY_DIR/$APP_NAME"

# Copy configuration files
echo "Deploying configuration files..."
sudo mkdir -p "$DEPLOY_DIR/configs"
sudo cp "configs/config.yaml" "$DEPLOY_DIR/configs/"
sudo cp "configs/config-${CONFIG_ENV}.yaml" "$DEPLOY_DIR/configs/" || true

# Create systemd service file
echo "Creating systemd service..."
sudo tee /etc/systemd/system/${SERVICE_NAME}.service > /dev/null <<EOF
[Unit]
Description=CMHK Communication Platform
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$DEPLOY_DIR
ExecStart=$DEPLOY_DIR/$APP_NAME
Restart=always
RestartSec=5
Environment=CMHK_SERVER_MODE=$CONFIG_ENV

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$DEPLOY_DIR

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and start service
echo "Starting service..."
sudo systemctl daemon-reload
sudo systemctl enable $SERVICE_NAME
sudo systemctl start $SERVICE_NAME

# Wait for service to start
sleep 5

# Check service status
if sudo systemctl is-active --quiet $SERVICE_NAME; then
    echo "✅ Deployment successful! Service is running."
    echo "Service status:"
    sudo systemctl status $SERVICE_NAME --no-pager -l
else
    echo "❌ Deployment failed! Service is not running."
    echo "Service logs:"
    sudo journalctl -u $SERVICE_NAME --no-pager -l
    exit 1
fi

# Health check
echo "Performing health check..."
sleep 10
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ Health check passed!"
else
    echo "⚠️  Health check failed, but service is running. Check logs."
fi

echo "Deployment completed successfully!"
echo "You can check logs with: sudo journalctl -u $SERVICE_NAME -f"
