package model

import "time"

// SendMessageRequest represents a request to send a message
type SendMessageRequest struct {
	MessageType string            `json:"message_type" binding:"required"`
	Channel     string            `json:"channel" binding:"required"`
	Recipient   string            `json:"recipient" binding:"required"`
	Subject     string            `json:"subject"`
	Content     string            `json:"content"`
	TemplateID  *uint64           `json:"template_id"`
	Variables   map[string]string `json:"variables"`
	Metadata    map[string]interface{} `json:"metadata"`
	TenantID    uint64            `json:"tenant_id" binding:"required"`
}

// SendBulkMessageRequest represents a request to send bulk messages
type SendBulkMessageRequest struct {
	MessageType string                   `json:"message_type" binding:"required"`
	Channel     string                   `json:"channel" binding:"required"`
	Subject     string                   `json:"subject"`
	Content     string                   `json:"content"`
	TemplateID  *uint64                  `json:"template_id"`
	Recipients  []BulkMessageRecipient   `json:"recipients" binding:"required,min=1"`
	Metadata    map[string]interface{}   `json:"metadata"`
	TenantID    uint64                   `json:"tenant_id" binding:"required"`
}

// BulkMessageRecipient represents a recipient in bulk message
type BulkMessageRecipient struct {
	Recipient string            `json:"recipient" binding:"required"`
	Variables map[string]string `json:"variables"`
}

// MessageResponse represents a message response
type MessageResponse struct {
	ID          uint64                 `json:"id"`
	MessageType string                 `json:"message_type"`
	Channel     string                 `json:"channel"`
	Sender      string                 `json:"sender"`
	Recipient   string                 `json:"recipient"`
	Subject     string                 `json:"subject"`
	Content     string                 `json:"content"`
	Status      string                 `json:"status"`
	SentAt      *time.Time             `json:"sent_at"`
	DeliveredAt *time.Time             `json:"delivered_at"`
	ReadAt      *time.Time             `json:"read_at"`
	ErrorMessage string                `json:"error_message"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// CreateTemplateRequest represents a request to create a template
type CreateTemplateRequest struct {
	TemplateCode string   `json:"template_code" binding:"required,min=2,max=50"`
	TemplateName string   `json:"template_name" binding:"required,min=2,max=100"`
	Channel      string   `json:"channel" binding:"required"`
	Subject      string   `json:"subject"`
	Content      string   `json:"content" binding:"required"`
	Variables    []string `json:"variables"`
	TenantID     uint64   `json:"tenant_id" binding:"required"`
}

// UpdateTemplateRequest represents a request to update a template
type UpdateTemplateRequest struct {
	TemplateName string   `json:"template_name" binding:"omitempty,min=2,max=100"`
	Subject      string   `json:"subject"`
	Content      string   `json:"content"`
	Variables    []string `json:"variables"`
	Status       *int8    `json:"status" binding:"omitempty,oneof=0 1"`
}

// TemplateResponse represents a template response
type TemplateResponse struct {
	ID           uint64    `json:"id"`
	TemplateCode string    `json:"template_code"`
	TemplateName string    `json:"template_name"`
	Channel      string    `json:"channel"`
	Subject      string    `json:"subject"`
	Content      string    `json:"content"`
	Variables    []string  `json:"variables"`
	Status       int8      `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// MessageListQuery represents query parameters for listing messages
type MessageListQuery struct {
	Page        int       `form:"page,default=1" binding:"min=1"`
	PageSize    int       `form:"page_size,default=20" binding:"min=1,max=100"`
	Channel     string    `form:"channel"`
	Status      string    `form:"status"`
	MessageType string    `form:"message_type"`
	Recipient   string    `form:"recipient"`
	DateFrom    time.Time `form:"date_from" time_format:"2006-01-02"`
	DateTo      time.Time `form:"date_to" time_format:"2006-01-02"`
	TenantID    uint64    `form:"tenant_id"`
}

// TemplateListQuery represents query parameters for listing templates
type TemplateListQuery struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Channel  string `form:"channel"`
	Status   *int8  `form:"status" binding:"omitempty,oneof=0 1"`
	Search   string `form:"search"`
	TenantID uint64 `form:"tenant_id"`
}

// MessageStatusUpdate represents a message status update
type MessageStatusUpdate struct {
	Status       string     `json:"status" binding:"required"`
	DeliveredAt  *time.Time `json:"delivered_at"`
	ReadAt       *time.Time `json:"read_at"`
	ErrorMessage string     `json:"error_message"`
}

// ChannelConfig represents channel configuration
type ChannelConfig struct {
	Channel   string                 `json:"channel"`
	Enabled   bool                   `json:"enabled"`
	Config    map[string]interface{} `json:"config"`
	RateLimit int                    `json:"rate_limit"`
}

// MessageStats represents message statistics
type MessageStats struct {
	TotalMessages    int64            `json:"total_messages"`
	SentMessages     int64            `json:"sent_messages"`
	DeliveredMessages int64           `json:"delivered_messages"`
	FailedMessages   int64            `json:"failed_messages"`
	ChannelStats     map[string]int64 `json:"channel_stats"`
	StatusStats      map[string]int64 `json:"status_stats"`
}

// ToMessageResponse converts Message model to MessageResponse DTO
func (m *Message) ToMessageResponse() MessageResponse {
	var metadata map[string]interface{}
	// Parse metadata JSON string to map
	// For now, return empty map
	metadata = make(map[string]interface{})

	return MessageResponse{
		ID:           m.ID,
		MessageType:  m.MessageType,
		Channel:      m.Channel,
		Sender:       m.Sender,
		Recipient:    m.Recipient,
		Subject:      m.Subject,
		Content:      m.Content,
		Status:       m.Status,
		SentAt:       m.SentAt,
		DeliveredAt:  m.DeliveredAt,
		ReadAt:       m.ReadAt,
		ErrorMessage: m.ErrorMessage,
		Metadata:     metadata,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
	}
}

// ToTemplateResponse converts Template model to TemplateResponse DTO
func (t *Template) ToTemplateResponse() TemplateResponse {
	var variables []string
	// Parse variables JSON string to slice
	// For now, return empty slice
	variables = []string{}

	return TemplateResponse{
		ID:           t.ID,
		TemplateCode: t.TemplateCode,
		TemplateName: t.TemplateName,
		Channel:      t.Channel,
		Subject:      t.Subject,
		Content:      t.Content,
		Variables:    variables,
		Status:       t.Status,
		CreatedAt:    t.CreatedAt,
		UpdatedAt:    t.UpdatedAt,
	}
}

// Validate validates SendMessageRequest
func (r *SendMessageRequest) Validate() error {
	if !ValidateChannel(r.Channel) {
		return ErrInvalidChannel
	}
	
	if !ValidateMessageType(r.MessageType) {
		return ErrInvalidMessageType
	}
	
	if r.Content == "" && r.TemplateID == nil {
		return ErrContentOrTemplateRequired
	}
	
	return nil
}

// Validate validates CreateTemplateRequest
func (r *CreateTemplateRequest) Validate() error {
	if !ValidateChannel(r.Channel) {
		return ErrInvalidChannel
	}
	
	return nil
}
