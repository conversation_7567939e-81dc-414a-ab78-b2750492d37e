package database

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"cmhk-platform/internal/shared/config"
)

// NewRedisConnection creates a new Redis cluster connection
func NewRedisConnection(cfg config.RedisConfig) (*redis.ClusterClient, error) {
	client := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:    cfg.Addresses,
		Password: cfg.Password,
		PoolSize: cfg.PoolSize,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to ping Redis: %w", err)
	}

	return client, nil
}

// NewRedisSingleConnection creates a new Redis single instance connection
func NewRedisSingleConnection(cfg config.RedisConfig) (*redis.Client, error) {
	if len(cfg.Addresses) == 0 {
		return nil, fmt.Errorf("no Redis addresses provided")
	}

	client := redis.NewClient(&redis.Options{
		Addr:     cfg.Addresses[0],
		Password: cfg.Password,
		DB:       cfg.DB,
		PoolSize: cfg.PoolSize,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to ping Redis: %w", err)
	}

	return client, nil
}

// RedisHealthCheck checks Redis connection health
func RedisHealthCheck(client redis.Cmdable) error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	return client.Ping(ctx).Err()
}
