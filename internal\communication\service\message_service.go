package service

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"cmhk-platform/internal/communication/model"
	"cmhk-platform/internal/communication/repository"
	"cmhk-platform/pkg/logger"
)

// MessageService defines the interface for message business logic
type MessageService interface {
	SendMessage(ctx context.Context, req model.SendMessageRequest) (*model.Message, error)
	SendBulkMessage(ctx context.Context, req model.SendBulkMessageRequest) ([]*model.Message, error)
	GetMessage(ctx context.Context, id uint64) (*model.Message, error)
	ListMessages(ctx context.Context, query model.MessageListQuery) ([]*model.Message, int64, error)
	UpdateMessageStatus(ctx context.Context, id uint64, update model.MessageStatusUpdate) error
	RetryMessage(ctx context.Context, id uint64) error
	GetMessageStats(ctx context.Context, tenantID uint64) (*model.MessageStats, error)
	ProcessPendingMessages(ctx context.Context) error
}

// messageService implements MessageService interface
type messageService struct {
	messageRepo  repository.MessageRepository
	templateRepo repository.TemplateRepository
	channelSvc   ChannelService
	logger       *logger.Logger
}

// NewMessageService creates a new message service
func NewMessageService(
	messageRepo repository.MessageRepository,
	templateRepo repository.TemplateRepository,
	channelSvc ChannelService,
	logger *logger.Logger,
) MessageService {
	return &messageService{
		messageRepo:  messageRepo,
		templateRepo: templateRepo,
		channelSvc:   channelSvc,
		logger:       logger,
	}
}

// SendMessage sends a single message
func (s *messageService) SendMessage(ctx context.Context, req model.SendMessageRequest) (*model.Message, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// Prepare message content
	content := req.Content
	subject := req.Subject

	// If template is specified, render it
	if req.TemplateID != nil {
		template, err := s.templateRepo.GetByID(ctx, *req.TemplateID)
		if err != nil {
			return nil, err
		}

		if !template.IsActive() {
			return nil, model.ErrTemplateInactive
		}

		// Render template with variables
		renderedContent, err := s.renderTemplate(template.Content, req.Variables)
		if err != nil {
			return nil, fmt.Errorf("failed to render template: %w", err)
		}
		content = renderedContent

		if template.Subject != "" {
			renderedSubject, err := s.renderTemplate(template.Subject, req.Variables)
			if err != nil {
				return nil, fmt.Errorf("failed to render template subject: %w", err)
			}
			subject = renderedSubject
		}
	}

	// Create message
	message := &model.Message{
		TenantID:    req.TenantID,
		MessageType: req.MessageType,
		Channel:     req.Channel,
		Recipient:   req.Recipient,
		Subject:     subject,
		Content:     content,
		TemplateID:  req.TemplateID,
		Status:      model.StatusPending,
	}

	// Set metadata if provided
	if req.Metadata != nil {
		// Convert metadata to JSON string
		// For now, skip this implementation
	}

	// Save message
	if err := s.messageRepo.Create(ctx, message); err != nil {
		return nil, fmt.Errorf("failed to create message: %w", err)
	}

	// Send message asynchronously
	go s.sendMessageAsync(context.Background(), message)

	s.logger.Info("Message created successfully",
		zap.Uint64("message_id", message.ID),
		zap.String("channel", message.Channel),
		zap.String("recipient", message.Recipient))

	return message, nil
}

// SendBulkMessage sends multiple messages
func (s *messageService) SendBulkMessage(ctx context.Context, req model.SendBulkMessageRequest) ([]*model.Message, error) {
	// Validate request
	if !model.ValidateChannel(req.Channel) {
		return nil, model.ErrInvalidChannel
	}

	if !model.ValidateMessageType(req.MessageType) {
		return nil, model.ErrInvalidMessageType
	}

	var template *model.Template
	if req.TemplateID != nil {
		var err error
		template, err = s.templateRepo.GetByID(ctx, *req.TemplateID)
		if err != nil {
			return nil, err
		}

		if !template.IsActive() {
			return nil, model.ErrTemplateInactive
		}
	}

	// Create messages for each recipient
	messages := make([]*model.Message, len(req.Recipients))
	for i, recipient := range req.Recipients {
		content := req.Content
		subject := req.Subject

		// Render template if specified
		if template != nil {
			renderedContent, err := s.renderTemplate(template.Content, recipient.Variables)
			if err != nil {
				return nil, fmt.Errorf("failed to render template for recipient %s: %w", recipient.Recipient, err)
			}
			content = renderedContent

			if template.Subject != "" {
				renderedSubject, err := s.renderTemplate(template.Subject, recipient.Variables)
				if err != nil {
					return nil, fmt.Errorf("failed to render template subject for recipient %s: %w", recipient.Recipient, err)
				}
				subject = renderedSubject
			}
		}

		messages[i] = &model.Message{
			TenantID:    req.TenantID,
			MessageType: req.MessageType,
			Channel:     req.Channel,
			Recipient:   recipient.Recipient,
			Subject:     subject,
			Content:     content,
			TemplateID:  req.TemplateID,
			Status:      model.StatusPending,
		}
	}

	// Bulk create messages
	if err := s.messageRepo.BulkCreate(ctx, messages); err != nil {
		return nil, fmt.Errorf("failed to bulk create messages: %w", err)
	}

	// Send messages asynchronously
	for _, message := range messages {
		go s.sendMessageAsync(context.Background(), message)
	}

	s.logger.Info("Bulk messages created successfully",
		zap.Int("count", len(messages)),
		zap.String("channel", req.Channel))

	return messages, nil
}

// GetMessage retrieves a message by ID
func (s *messageService) GetMessage(ctx context.Context, id uint64) (*model.Message, error) {
	return s.messageRepo.GetByID(ctx, id)
}

// ListMessages retrieves messages with pagination and filtering
func (s *messageService) ListMessages(ctx context.Context, query model.MessageListQuery) ([]*model.Message, int64, error) {
	return s.messageRepo.List(ctx, query)
}

// UpdateMessageStatus updates message status
func (s *messageService) UpdateMessageStatus(ctx context.Context, id uint64, update model.MessageStatusUpdate) error {
	// Get message to verify it exists
	message, err := s.messageRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// Update status
	if err := s.messageRepo.UpdateStatus(ctx, id, update.Status, update.ErrorMessage); err != nil {
		return fmt.Errorf("failed to update message status: %w", err)
	}

	s.logger.Info("Message status updated",
		zap.Uint64("message_id", message.ID),
		zap.String("old_status", message.Status),
		zap.String("new_status", update.Status))

	return nil
}

// RetryMessage retries a failed message
func (s *messageService) RetryMessage(ctx context.Context, id uint64) error {
	message, err := s.messageRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if !message.CanRetry() {
		return model.ErrMessageCannotBeRetried
	}

	// Reset status to pending
	if err := s.messageRepo.UpdateStatus(ctx, id, model.StatusPending, ""); err != nil {
		return fmt.Errorf("failed to reset message status: %w", err)
	}

	// Send message asynchronously
	go s.sendMessageAsync(context.Background(), message)

	s.logger.Info("Message retry initiated",
		zap.Uint64("message_id", message.ID))

	return nil
}

// GetMessageStats retrieves message statistics
func (s *messageService) GetMessageStats(ctx context.Context, tenantID uint64) (*model.MessageStats, error) {
	return s.messageRepo.GetMessageStats(ctx, tenantID)
}

// ProcessPendingMessages processes pending messages
func (s *messageService) ProcessPendingMessages(ctx context.Context) error {
	messages, err := s.messageRepo.GetPendingMessages(ctx, 100)
	if err != nil {
		return fmt.Errorf("failed to get pending messages: %w", err)
	}

	for _, message := range messages {
		go s.sendMessageAsync(context.Background(), message)
	}

	s.logger.Info("Processing pending messages",
		zap.Int("count", len(messages)))

	return nil
}

// sendMessageAsync sends a message asynchronously
func (s *messageService) sendMessageAsync(ctx context.Context, message *model.Message) {
	// Update status to sending
	if err := s.messageRepo.UpdateStatus(ctx, message.ID, model.StatusSending, ""); err != nil {
		s.logger.Error("Failed to update message status to sending",
			zap.Uint64("message_id", message.ID),
			zap.Error(err))
		return
	}

	// Send through channel service
	err := s.channelSvc.SendMessage(ctx, message)
	if err != nil {
		// Update status to failed
		if updateErr := s.messageRepo.UpdateStatus(ctx, message.ID, model.StatusFailed, err.Error()); updateErr != nil {
			s.logger.Error("Failed to update message status to failed",
				zap.Uint64("message_id", message.ID),
				zap.Error(updateErr))
		}

		s.logger.Error("Failed to send message",
			zap.Uint64("message_id", message.ID),
			zap.String("channel", message.Channel),
			zap.Error(err))
		return
	}

	// Update status to sent
	if err := s.messageRepo.UpdateStatus(ctx, message.ID, model.StatusSent, ""); err != nil {
		s.logger.Error("Failed to update message status to sent",
			zap.Uint64("message_id", message.ID),
			zap.Error(err))
		return
	}

	s.logger.Info("Message sent successfully",
		zap.Uint64("message_id", message.ID),
		zap.String("channel", message.Channel),
		zap.String("recipient", message.Recipient))
}

// renderTemplate renders a template with variables
func (s *messageService) renderTemplate(template string, variables map[string]string) (string, error) {
	// Simple template rendering - replace {{variable}} with values
	// In production, use a proper template engine like text/template
	result := template

	if variables != nil {
		for key, value := range variables {
			placeholder := "{{" + key + "}}"
			// Simple string replacement
			// In production, use proper template parsing
			for i := 0; i < len(result); i++ {
				if i+len(placeholder) <= len(result) && result[i:i+len(placeholder)] == placeholder {
					result = result[:i] + value + result[i+len(placeholder):]
					i += len(value) - 1
				}
			}
		}
	}

	return result, nil
}
