package model

import (
	"time"
	"gorm.io/gorm"
)

// Report represents a report definition
type Report struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_code" json:"tenant_id"`
	ReportCode  string         `gorm:"size:50;not null;uniqueIndex:uk_tenant_report_code" json:"report_code"`
	ReportName  string         `gorm:"size:100;not null" json:"report_name"`
	Description string         `gorm:"type:text" json:"description"`
	ReportType  string         `gorm:"size:20;not null" json:"report_type"`
	DataSource  string         `gorm:"size:50;not null" json:"data_source"`
	Query       string         `gorm:"type:text;not null" json:"query"`
	Parameters  string         `gorm:"type:json" json:"parameters"`
	Schedule    string         `gorm:"type:json" json:"schedule"`
	Format      string         `gorm:"size:20;default:json" json:"format"`
	Status      int8           `gorm:"default:1" json:"status"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Executions []ReportExecution `gorm:"foreignKey:ReportID" json:"executions,omitempty"`
}

// ReportExecution represents a report execution record
type ReportExecution struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_report" json:"tenant_id"`
	ReportID    uint64         `gorm:"not null;index:idx_tenant_report" json:"report_id"`
	Status      string         `gorm:"size:20;default:pending" json:"status"`
	StartedAt   *time.Time     `json:"started_at"`
	CompletedAt *time.Time     `json:"completed_at"`
	Duration    int64          `gorm:"default:0" json:"duration"` // milliseconds
	ResultSize  int64          `gorm:"default:0" json:"result_size"`
	ResultPath  string         `gorm:"size:255" json:"result_path"`
	ErrorMessage string        `gorm:"type:text" json:"error_message"`
	Parameters  string         `gorm:"type:json" json:"parameters"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// Metric represents a system metric
type Metric struct {
	ID          uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64    `gorm:"not null;index:idx_tenant_metric" json:"tenant_id"`
	MetricName  string    `gorm:"size:100;not null;index:idx_tenant_metric" json:"metric_name"`
	MetricType  string    `gorm:"size:20;not null" json:"metric_type"`
	Value       float64   `gorm:"type:decimal(15,4)" json:"value"`
	Unit        string    `gorm:"size:20" json:"unit"`
	Tags        string    `gorm:"type:json" json:"tags"`
	Timestamp   time.Time `gorm:"index:idx_timestamp" json:"timestamp"`
	CreatedAt   time.Time `json:"created_at"`
}

// Dashboard represents a dashboard configuration
type Dashboard struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_code" json:"tenant_id"`
	DashboardCode string       `gorm:"size:50;not null;uniqueIndex:uk_tenant_dashboard_code" json:"dashboard_code"`
	DashboardName string       `gorm:"size:100;not null" json:"dashboard_name"`
	Description string         `gorm:"type:text" json:"description"`
	Layout      string         `gorm:"type:json;not null" json:"layout"`
	Widgets     string         `gorm:"type:json;not null" json:"widgets"`
	IsPublic    bool           `gorm:"default:false" json:"is_public"`
	Status      int8           `gorm:"default:1" json:"status"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// Alert represents an alert rule
type Alert struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_alert" json:"tenant_id"`
	AlertName   string         `gorm:"size:100;not null" json:"alert_name"`
	Description string         `gorm:"type:text" json:"description"`
	MetricName  string         `gorm:"size:100;not null" json:"metric_name"`
	Condition   string         `gorm:"type:json;not null" json:"condition"`
	Threshold   float64        `gorm:"type:decimal(15,4)" json:"threshold"`
	Severity    string         `gorm:"size:20;not null" json:"severity"`
	Actions     string         `gorm:"type:json" json:"actions"`
	Status      int8           `gorm:"default:1" json:"status"`
	LastTriggered *time.Time   `json:"last_triggered"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// AlertHistory represents alert trigger history
type AlertHistory struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint64    `gorm:"not null;index:idx_tenant_alert" json:"tenant_id"`
	AlertID   uint64    `gorm:"not null;index:idx_tenant_alert" json:"alert_id"`
	Value     float64   `gorm:"type:decimal(15,4)" json:"value"`
	Threshold float64   `gorm:"type:decimal(15,4)" json:"threshold"`
	Severity  string    `gorm:"size:20;not null" json:"severity"`
	Message   string    `gorm:"type:text" json:"message"`
	Status    string    `gorm:"size:20;not null" json:"status"`
	CreatedAt time.Time `gorm:"index:idx_created_at" json:"created_at"`
}

// Report types
const (
	ReportTypeTable     = "table"
	ReportTypeChart     = "chart"
	ReportTypeSummary   = "summary"
	ReportTypeExport    = "export"
)

// Report statuses
const (
	ReportStatusInactive = 0
	ReportStatusActive   = 1
)

// Execution statuses
const (
	ExecutionStatusPending   = "pending"
	ExecutionStatusRunning   = "running"
	ExecutionStatusCompleted = "completed"
	ExecutionStatusFailed    = "failed"
)

// Metric types
const (
	MetricTypeCounter   = "counter"
	MetricTypeGauge     = "gauge"
	MetricTypeHistogram = "histogram"
	MetricTypeSummary   = "summary"
)

// Alert severities
const (
	SeverityLow      = "low"
	SeverityMedium   = "medium"
	SeverityHigh     = "high"
	SeverityCritical = "critical"
)

// Alert statuses
const (
	AlertStatusInactive = 0
	AlertStatusActive   = 1
)

// Alert history statuses
const (
	AlertHistoryStatusTriggered = "triggered"
	AlertHistoryStatusResolved  = "resolved"
)

// Data sources
const (
	DataSourceMySQL     = "mysql"
	DataSourceClickHouse = "clickhouse"
	DataSourceRedis     = "redis"
	DataSourceAPI       = "api"
)

// Report formats
const (
	FormatJSON = "json"
	FormatCSV  = "csv"
	FormatXLSX = "xlsx"
	FormatPDF  = "pdf"
)

// TableName returns the table name for Report
func (Report) TableName() string {
	return "reports"
}

// TableName returns the table name for ReportExecution
func (ReportExecution) TableName() string {
	return "report_executions"
}

// TableName returns the table name for Metric
func (Metric) TableName() string {
	return "metrics"
}

// TableName returns the table name for Dashboard
func (Dashboard) TableName() string {
	return "dashboards"
}

// TableName returns the table name for Alert
func (Alert) TableName() string {
	return "alerts"
}

// TableName returns the table name for AlertHistory
func (AlertHistory) TableName() string {
	return "alert_history"
}

// IsActive checks if report is active
func (r *Report) IsActive() bool {
	return r.Status == ReportStatusActive
}

// IsCompleted checks if execution is completed
func (e *ReportExecution) IsCompleted() bool {
	return e.Status == ExecutionStatusCompleted
}

// IsFailed checks if execution failed
func (e *ReportExecution) IsFailed() bool {
	return e.Status == ExecutionStatusFailed
}

// GetDurationSeconds returns execution duration in seconds
func (e *ReportExecution) GetDurationSeconds() float64 {
	return float64(e.Duration) / 1000.0
}

// IsActive checks if dashboard is active
func (d *Dashboard) IsActive() bool {
	return d.Status == 1
}

// IsActive checks if alert is active
func (a *Alert) IsActive() bool {
	return a.Status == AlertStatusActive
}

// ValidateReportType validates if report type is supported
func ValidateReportType(reportType string) bool {
	supportedTypes := []string{
		ReportTypeTable,
		ReportTypeChart,
		ReportTypeSummary,
		ReportTypeExport,
	}
	
	for _, supported := range supportedTypes {
		if reportType == supported {
			return true
		}
	}
	
	return false
}

// ValidateDataSource validates if data source is supported
func ValidateDataSource(dataSource string) bool {
	supportedSources := []string{
		DataSourceMySQL,
		DataSourceClickHouse,
		DataSourceRedis,
		DataSourceAPI,
	}
	
	for _, supported := range supportedSources {
		if dataSource == supported {
			return true
		}
	}
	
	return false
}

// ValidateMetricType validates if metric type is supported
func ValidateMetricType(metricType string) bool {
	supportedTypes := []string{
		MetricTypeCounter,
		MetricTypeGauge,
		MetricTypeHistogram,
		MetricTypeSummary,
	}
	
	for _, supported := range supportedTypes {
		if metricType == supported {
			return true
		}
	}
	
	return false
}
