package model

import (
	"time"
	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID               uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	Username         string         `gorm:"uniqueIndex;size:50;not null" json:"username"`
	Email            string         `gorm:"uniqueIndex;size:100" json:"email"`
	Phone            string         `gorm:"uniqueIndex;size:20" json:"phone"`
	PasswordHash     string         `gorm:"size:255;not null" json:"-"`
	Salt             string         `gorm:"size:32;not null" json:"-"`
	Status           int8           `gorm:"default:1" json:"status"`
	TenantID         uint64         `gorm:"not null;index:idx_tenant_status" json:"tenant_id"`
	LastLoginAt      *time.Time     `gorm:"index" json:"last_login_at"`
	FailedLoginCount int            `gorm:"default:0" json:"failed_login_count"`
	LockedUntil      *time.Time     `json:"locked_until"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Roles []Role `gorm:"many2many:user_roles;" json:"roles,omitempty"`
}

// Role represents a role in the system
type Role struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;uniqueIndex:uk_tenant_code" json:"tenant_id"`
	RoleCode    string         `gorm:"size:50;not null;uniqueIndex:uk_tenant_code" json:"role_code"`
	RoleName    string         `gorm:"size:100;not null" json:"role_name"`
	Permissions string         `gorm:"type:json;not null" json:"permissions"`
	Status      int8           `gorm:"default:1" json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// UserRole represents the many-to-many relationship between users and roles
type UserRole struct {
	ID        uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    uint64     `gorm:"not null;uniqueIndex:uk_user_role" json:"user_id"`
	RoleID    uint64     `gorm:"not null;uniqueIndex:uk_user_role" json:"role_id"`
	GrantedBy uint64     `json:"granted_by"`
	GrantedAt time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"granted_at"`
	ExpiresAt *time.Time `json:"expires_at"`
}

// User status constants
const (
	UserStatusInactive = 0
	UserStatusActive   = 1
	UserStatusLocked   = 2
	UserStatusSuspended = 3
)

// Role status constants
const (
	RoleStatusInactive = 0
	RoleStatusActive   = 1
)

// TableName returns the table name for User
func (User) TableName() string {
	return "users"
}

// TableName returns the table name for Role
func (Role) TableName() string {
	return "roles"
}

// TableName returns the table name for UserRole
func (UserRole) TableName() string {
	return "user_roles"
}

// IsActive checks if user is active
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsLocked checks if user is locked
func (u *User) IsLocked() bool {
	return u.Status == UserStatusLocked || (u.LockedUntil != nil && u.LockedUntil.After(time.Now()))
}

// GetRoleCodes returns role codes for the user
func (u *User) GetRoleCodes() []string {
	codes := make([]string, len(u.Roles))
	for i, role := range u.Roles {
		codes[i] = role.RoleCode
	}
	return codes
}
