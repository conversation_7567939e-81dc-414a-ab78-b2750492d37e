package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"

	"cmhk-platform/internal/shared/auth"
	"cmhk-platform/internal/shared/cache"
	"cmhk-platform/internal/user/model"
	"cmhk-platform/pkg/logger"
)

// MockUserRepository is a mock implementation of UserRepository
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uint64) (*model.User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetByUsername(ctx context.Context, username string) (*model.User, error) {
	args := m.Called(ctx, username)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetByPhone(ctx context.Context, phone string) (*model.User, error) {
	args := m.Called(ctx, phone)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, id uint64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) List(ctx context.Context, query model.UserListQuery) ([]*model.User, int64, error) {
	args := m.Called(ctx, query)
	return args.Get(0).([]*model.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) AssignRole(ctx context.Context, userID, roleID, grantedBy uint64) error {
	args := m.Called(ctx, userID, roleID, grantedBy)
	return args.Error(0)
}

func (m *MockUserRepository) RemoveRole(ctx context.Context, userID, roleID uint64) error {
	args := m.Called(ctx, userID, roleID)
	return args.Error(0)
}

func (m *MockUserRepository) GetUserRoles(ctx context.Context, userID uint64) ([]*model.Role, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*model.Role), args.Error(1)
}

func (m *MockUserRepository) UpdateLastLogin(ctx context.Context, userID uint64) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockUserRepository) IncrementFailedLoginCount(ctx context.Context, userID uint64) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockUserRepository) ResetFailedLoginCount(ctx context.Context, userID uint64) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockUserRepository) LockUser(ctx context.Context, userID uint64) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockUserRepository) UnlockUser(ctx context.Context, userID uint64) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

// MockRoleRepository is a mock implementation of RoleRepository
type MockRoleRepository struct {
	mock.Mock
}

func (m *MockRoleRepository) Create(ctx context.Context, role *model.Role) error {
	args := m.Called(ctx, role)
	return args.Error(0)
}

func (m *MockRoleRepository) GetByID(ctx context.Context, id uint64) (*model.Role, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Role), args.Error(1)
}

func (m *MockRoleRepository) GetByCode(ctx context.Context, tenantID uint64, code string) (*model.Role, error) {
	args := m.Called(ctx, tenantID, code)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Role), args.Error(1)
}

func (m *MockRoleRepository) Update(ctx context.Context, role *model.Role) error {
	args := m.Called(ctx, role)
	return args.Error(0)
}

func (m *MockRoleRepository) Delete(ctx context.Context, id uint64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockRoleRepository) List(ctx context.Context, query model.RoleListQuery) ([]*model.Role, int64, error) {
	args := m.Called(ctx, query)
	return args.Get(0).([]*model.Role), args.Get(1).(int64), args.Error(2)
}

func (m *MockRoleRepository) HasUsersAssigned(ctx context.Context, roleID uint64) (bool, error) {
	args := m.Called(ctx, roleID)
	return args.Bool(0), args.Error(1)
}

// Test setup helper
func setupUserServiceTest() (*userService, *MockUserRepository, *MockRoleRepository) {
	mockUserRepo := &MockUserRepository{}
	mockRoleRepo := &MockRoleRepository{}
	
	jwtManager := auth.NewJWTManager("test-secret", time.Hour, time.Hour*24)
	cacheManager := &cache.CacheManager{} // Mock cache manager
	logger := logger.NewLogger(&logger.Config{Level: "info"})
	
	service := &userService{
		userRepo:   mockUserRepo,
		roleRepo:   mockRoleRepo,
		jwtManager: jwtManager,
		cache:      cacheManager,
		logger:     logger,
	}
	
	return service, mockUserRepo, mockRoleRepo
}

func TestUserService_CreateUser(t *testing.T) {
	service, mockUserRepo, _ := setupUserServiceTest()
	ctx := context.Background()

	t.Run("successful user creation", func(t *testing.T) {
		req := model.CreateUserRequest{
			Username: "testuser",
			Email:    "<EMAIL>",
			Phone:    "+1234567890",
			Password: "password123",
			TenantID: 1,
		}

		// Mock repository calls
		mockUserRepo.On("GetByUsername", ctx, "testuser").Return(nil, model.ErrUserNotFound)
		mockUserRepo.On("GetByEmail", ctx, "<EMAIL>").Return(nil, model.ErrUserNotFound)
		mockUserRepo.On("GetByPhone", ctx, "+1234567890").Return(nil, model.ErrUserNotFound)
		mockUserRepo.On("Create", ctx, mock.AnythingOfType("*model.User")).Return(nil)

		user, err := service.CreateUser(ctx, req)

		assert.NoError(t, err)
		assert.NotNil(t, user)
		assert.Equal(t, "testuser", user.Username)
		assert.Equal(t, "<EMAIL>", user.Email)
		assert.Equal(t, model.UserStatusActive, user.Status)
		mockUserRepo.AssertExpectations(t)
	})

	t.Run("user already exists", func(t *testing.T) {
		req := model.CreateUserRequest{
			Username: "existinguser",
			Email:    "<EMAIL>",
			Phone:    "+1234567890",
			Password: "password123",
			TenantID: 1,
		}

		existingUser := &model.User{
			ID:       1,
			Username: "existinguser",
			Email:    "<EMAIL>",
		}

		mockUserRepo.On("GetByUsername", ctx, "existinguser").Return(existingUser, nil)

		user, err := service.CreateUser(ctx, req)

		assert.Error(t, err)
		assert.Equal(t, model.ErrUserAlreadyExists, err)
		assert.Nil(t, user)
		mockUserRepo.AssertExpectations(t)
	})
}

func TestUserService_Login(t *testing.T) {
	service, mockUserRepo, _ := setupUserServiceTest()
	ctx := context.Background()

	t.Run("successful login", func(t *testing.T) {
		req := model.LoginRequest{
			Username: "testuser",
			Password: "password123",
		}

		// Create a user with hashed password
		hashedPassword, _ := service.hashPassword("password123")
		user := &model.User{
			ID:           1,
			Username:     "testuser",
			Email:        "<EMAIL>",
			PasswordHash: hashedPassword,
			Status:       model.UserStatusActive,
			TenantID:     1,
			Roles:        []model.Role{},
		}

		mockUserRepo.On("GetByUsername", ctx, "testuser").Return(user, nil)
		mockUserRepo.On("ResetFailedLoginCount", ctx, uint64(1)).Return(nil)
		mockUserRepo.On("UpdateLastLogin", ctx, uint64(1)).Return(nil)

		response, err := service.Login(ctx, req)

		assert.NoError(t, err)
		assert.NotNil(t, response)
		assert.NotEmpty(t, response.AccessToken)
		assert.NotEmpty(t, response.RefreshToken)
		assert.Equal(t, "testuser", response.User.Username)
		mockUserRepo.AssertExpectations(t)
	})

	t.Run("invalid credentials", func(t *testing.T) {
		req := model.LoginRequest{
			Username: "testuser",
			Password: "wrongpassword",
		}

		hashedPassword, _ := service.hashPassword("password123")
		user := &model.User{
			ID:           1,
			Username:     "testuser",
			PasswordHash: hashedPassword,
			Status:       model.UserStatusActive,
		}

		mockUserRepo.On("GetByUsername", ctx, "testuser").Return(user, nil)
		mockUserRepo.On("IncrementFailedLoginCount", ctx, uint64(1)).Return(nil)

		response, err := service.Login(ctx, req)

		assert.Error(t, err)
		assert.Equal(t, model.ErrInvalidCredentials, err)
		assert.Nil(t, response)
		mockUserRepo.AssertExpectations(t)
	})

	t.Run("user not found", func(t *testing.T) {
		req := model.LoginRequest{
			Username: "nonexistent",
			Password: "password123",
		}

		mockUserRepo.On("GetByUsername", ctx, "nonexistent").Return(nil, model.ErrUserNotFound)

		response, err := service.Login(ctx, req)

		assert.Error(t, err)
		assert.Equal(t, model.ErrInvalidCredentials, err)
		assert.Nil(t, response)
		mockUserRepo.AssertExpectations(t)
	})
}

// Helper method for password hashing in tests
func (s *userService) hashPassword(password string) (string, error) {
	// This is a simplified version for testing
	// In real implementation, use proper bcrypt or similar
	return "hashed_" + password, nil
}
