package model

import (
	"time"
	"gorm.io/gorm"
)

// Customer represents a customer in the system
type Customer struct {
	ID           uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID     uint64         `gorm:"not null;uniqueIndex:uk_tenant_code" json:"tenant_id"`
	CustomerCode string         `gorm:"size:50;uniqueIndex:uk_tenant_code" json:"customer_code"`
	FirstName    string         `gorm:"size:50" json:"first_name"`
	LastName     string         `gorm:"size:50" json:"last_name"`
	Email        string         `gorm:"size:100;index:idx_email" json:"email"`
	Phone        string         `gorm:"size:20;index:idx_phone" json:"phone"`
	DateOfBirth  *time.Time     `json:"date_of_birth"`
	Gender       string         `gorm:"type:enum('male','female','other')" json:"gender"`
	Status       int8           `gorm:"default:1;index:idx_tenant_status" json:"status"`
	ProfileData  string         `gorm:"type:json" json:"profile_data"`
	Tags         string         `gorm:"type:json" json:"tags"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Groups []CustomerGroup `gorm:"many2many:customer_group_members;" json:"groups,omitempty"`
}

// CustomerGroup represents a customer group
type CustomerGroup struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;uniqueIndex:uk_tenant_group_code" json:"tenant_id"`
	GroupCode   string         `gorm:"size:50;not null;uniqueIndex:uk_tenant_group_code" json:"group_code"`
	GroupName   string         `gorm:"size:100;not null" json:"group_name"`
	Description string         `gorm:"type:text" json:"description"`
	Conditions  string         `gorm:"type:json" json:"conditions"`
	Status      int8           `gorm:"default:1" json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// CustomerGroupMember represents the many-to-many relationship between customers and groups
type CustomerGroupMember struct {
	ID         uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	CustomerID uint64    `gorm:"not null;uniqueIndex:uk_customer_group" json:"customer_id"`
	GroupID    uint64    `gorm:"not null;uniqueIndex:uk_customer_group" json:"group_id"`
	JoinedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"joined_at"`
}

// CustomerTag represents a customer tag
type CustomerTag struct {
	ID        uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint64         `gorm:"not null;uniqueIndex:uk_tenant_tag_name" json:"tenant_id"`
	TagName   string         `gorm:"size:50;not null;uniqueIndex:uk_tenant_tag_name" json:"tag_name"`
	TagColor  string         `gorm:"size:7;default:#007bff" json:"tag_color"`
	Status    int8           `gorm:"default:1" json:"status"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// CustomerActivity represents customer activity log
type CustomerActivity struct {
	ID           uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID     uint64    `gorm:"not null;index:idx_tenant_customer" json:"tenant_id"`
	CustomerID   uint64    `gorm:"not null;index:idx_tenant_customer" json:"customer_id"`
	ActivityType string    `gorm:"size:50;not null" json:"activity_type"`
	Description  string    `gorm:"type:text" json:"description"`
	Metadata     string    `gorm:"type:json" json:"metadata"`
	CreatedAt    time.Time `gorm:"index:idx_created_at" json:"created_at"`
}

// Customer status constants
const (
	CustomerStatusInactive = 0
	CustomerStatusActive   = 1
	CustomerStatusBlocked  = 2
)

// Customer group status constants
const (
	GroupStatusInactive = 0
	GroupStatusActive   = 1
)

// Customer tag status constants
const (
	TagStatusInactive = 0
	TagStatusActive   = 1
)

// Gender constants
const (
	GenderMale   = "male"
	GenderFemale = "female"
	GenderOther  = "other"
)

// Activity type constants
const (
	ActivityTypeRegistered    = "registered"
	ActivityTypeLogin         = "login"
	ActivityTypeProfileUpdate = "profile_update"
	ActivityTypeMessageSent   = "message_sent"
	ActivityTypeGroupJoined   = "group_joined"
	ActivityTypeGroupLeft     = "group_left"
	ActivityTypeTagAdded      = "tag_added"
	ActivityTypeTagRemoved    = "tag_removed"
)

// TableName returns the table name for Customer
func (Customer) TableName() string {
	return "customers"
}

// TableName returns the table name for CustomerGroup
func (CustomerGroup) TableName() string {
	return "customer_groups"
}

// TableName returns the table name for CustomerGroupMember
func (CustomerGroupMember) TableName() string {
	return "customer_group_members"
}

// TableName returns the table name for CustomerTag
func (CustomerTag) TableName() string {
	return "customer_tags"
}

// TableName returns the table name for CustomerActivity
func (CustomerActivity) TableName() string {
	return "customer_activities"
}

// IsActive checks if customer is active
func (c *Customer) IsActive() bool {
	return c.Status == CustomerStatusActive
}

// IsBlocked checks if customer is blocked
func (c *Customer) IsBlocked() bool {
	return c.Status == CustomerStatusBlocked
}

// GetFullName returns customer's full name
func (c *Customer) GetFullName() string {
	if c.FirstName != "" && c.LastName != "" {
		return c.FirstName + " " + c.LastName
	}
	if c.FirstName != "" {
		return c.FirstName
	}
	if c.LastName != "" {
		return c.LastName
	}
	return c.CustomerCode
}

// IsActive checks if group is active
func (g *CustomerGroup) IsActive() bool {
	return g.Status == GroupStatusActive
}

// IsActive checks if tag is active
func (t *CustomerTag) IsActive() bool {
	return t.Status == TagStatusActive
}

// ValidateGender validates if gender is valid
func ValidateGender(gender string) bool {
	validGenders := []string{GenderMale, GenderFemale, GenderOther}
	for _, valid := range validGenders {
		if gender == valid {
			return true
		}
	}
	return false
}

// ValidateActivityType validates if activity type is valid
func ValidateActivityType(activityType string) bool {
	validTypes := []string{
		ActivityTypeRegistered,
		ActivityTypeLogin,
		ActivityTypeProfileUpdate,
		ActivityTypeMessageSent,
		ActivityTypeGroupJoined,
		ActivityTypeGroupLeft,
		ActivityTypeTagAdded,
		ActivityTypeTagRemoved,
	}
	for _, valid := range validTypes {
		if activityType == valid {
			return true
		}
	}
	return false
}
