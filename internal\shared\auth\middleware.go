package auth

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"cmhk-platform/internal/shared/types"
)

// AuthMiddleware creates an authentication middleware
func AuthMiddleware(jwtManager *JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			types.ErrorResponse(c, http.StatusUnauthorized, 
				types.ErrorCodeUnauthorized, "Authorization header is required")
			c.Abort()
			return
		}

		token := ExtractTokenFromHeader(authHeader)
		if token == "" {
			types.ErrorResponse(c, http.StatusUnauthorized, 
				types.ErrorCodeUnauthorized, "Invalid authorization header format")
			c.Abort()
			return
		}

		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			types.ErrorResponse(c, http.StatusUnauthorized, 
				types.ErrorCodeUnauthorized, "Invalid or expired token")
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("tenant_id", claims.TenantID)
		c.Set("roles", claims.Roles)

		c.Next()
	}
}

// OptionalAuthMiddleware creates an optional authentication middleware
func OptionalAuthMiddleware(jwtManager *JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		token := ExtractTokenFromHeader(authHeader)
		if token == "" {
			c.Next()
			return
		}

		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			c.Next()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("tenant_id", claims.TenantID)
		c.Set("roles", claims.Roles)

		c.Next()
	}
}

// RoleMiddleware creates a role-based authorization middleware
func RoleMiddleware(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRoles, exists := c.Get("roles")
		if !exists {
			types.ErrorResponse(c, http.StatusForbidden, 
				types.ErrorCodeForbidden, "User roles not found")
			c.Abort()
			return
		}

		roles, ok := userRoles.([]string)
		if !ok {
			types.ErrorResponse(c, http.StatusForbidden, 
				types.ErrorCodeForbidden, "Invalid user roles")
			c.Abort()
			return
		}

		if !hasRequiredRole(roles, requiredRoles) {
			types.ErrorResponse(c, http.StatusForbidden, 
				types.ErrorCodeForbidden, "Insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}

// TenantMiddleware creates a tenant-based authorization middleware
func TenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		tenantID, exists := c.Get("tenant_id")
		if !exists {
			types.ErrorResponse(c, http.StatusForbidden, 
				types.ErrorCodeForbidden, "Tenant ID not found")
			c.Abort()
			return
		}

		// Add tenant ID to request context for database queries
		c.Set("current_tenant_id", tenantID)
		c.Next()
	}
}

// hasRequiredRole checks if user has any of the required roles
func hasRequiredRole(userRoles, requiredRoles []string) bool {
	if len(requiredRoles) == 0 {
		return true
	}

	roleMap := make(map[string]bool)
	for _, role := range userRoles {
		roleMap[strings.ToLower(role)] = true
	}

	for _, requiredRole := range requiredRoles {
		if roleMap[strings.ToLower(requiredRole)] {
			return true
		}
	}

	return false
}

// GetCurrentUserID extracts current user ID from context
func GetCurrentUserID(c *gin.Context) (uint64, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	
	id, ok := userID.(uint64)
	return id, ok
}

// GetCurrentTenantID extracts current tenant ID from context
func GetCurrentTenantID(c *gin.Context) (uint64, bool) {
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		return 0, false
	}
	
	id, ok := tenantID.(uint64)
	return id, ok
}

// GetCurrentUsername extracts current username from context
func GetCurrentUsername(c *gin.Context) (string, bool) {
	username, exists := c.Get("username")
	if !exists {
		return "", false
	}
	
	name, ok := username.(string)
	return name, ok
}

// GetCurrentUserRoles extracts current user roles from context
func GetCurrentUserRoles(c *gin.Context) ([]string, bool) {
	roles, exists := c.Get("roles")
	if !exists {
		return nil, false
	}
	
	userRoles, ok := roles.([]string)
	return userRoles, ok
}
