package middleware

import (
	"bytes"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"cmhk-platform/pkg/logger"
)

// LoggingConfig represents logging middleware configuration
type LoggingConfig struct {
	Logger        *logger.Logger
	SkipPaths     []string
	LogRequestBody bool
	LogResponseBody bool
}

// responseWriter wraps gin.ResponseWriter to capture response body
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// Logging returns a logging middleware with default configuration
func Logging(log *logger.Logger) gin.HandlerFunc {
	return LoggingWithConfig(LoggingConfig{
		Logger:          log,
		SkipPaths:       []string{"/health", "/metrics"},
		LogRequestBody:  false,
		LogResponseBody: false,
	})
}

// LoggingWithConfig returns a logging middleware with custom configuration
func LoggingWithConfig(config LoggingConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip logging for specified paths
		path := c.Request.URL.Path
		for _, skipPath := range config.SkipPaths {
			if path == skipPath {
				c.Next()
				return
			}
		}

		start := time.Now()
		
		// Capture request body if enabled
		var requestBody string
		if config.LogRequestBody && c.Request.Body != nil {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil {
				requestBody = string(bodyBytes)
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// Wrap response writer to capture response body
		var responseBody string
		if config.LogResponseBody {
			writer := &responseWriter{
				ResponseWriter: c.Writer,
				body:          bytes.NewBufferString(""),
			}
			c.Writer = writer
			defer func() {
				responseBody = writer.body.String()
			}()
		}

		// Process request
		c.Next()

		// Calculate duration
		duration := time.Since(start)

		// Get request ID
		requestID := getRequestID(c)

		// Log request information
		fields := []zap.Field{
			zap.String("method", c.Request.Method),
			zap.String("path", path),
			zap.String("query", c.Request.URL.RawQuery),
			zap.String("user_agent", c.Request.UserAgent()),
			zap.String("client_ip", c.ClientIP()),
			zap.Int("status_code", c.Writer.Status()),
			zap.Int64("duration_ms", duration.Milliseconds()),
			zap.Int("response_size", c.Writer.Size()),
		}

		if requestID != "" {
			fields = append(fields, zap.String("request_id", requestID))
		}

		if config.LogRequestBody && requestBody != "" {
			fields = append(fields, zap.String("request_body", requestBody))
		}

		if config.LogResponseBody && responseBody != "" {
			fields = append(fields, zap.String("response_body", responseBody))
		}

		// Add error information if present
		if len(c.Errors) > 0 {
			fields = append(fields, zap.String("errors", c.Errors.String()))
		}

		// Log with appropriate level based on status code
		statusCode := c.Writer.Status()
		switch {
		case statusCode >= 500:
			config.Logger.Error("HTTP Request", fields...)
		case statusCode >= 400:
			config.Logger.Warn("HTTP Request", fields...)
		default:
			config.Logger.Info("HTTP Request", fields...)
		}
	}
}

// getRequestID extracts request ID from context
func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return ""
}
