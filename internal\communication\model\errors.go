package model

import "errors"

// Message related errors
var (
	ErrMessageNotFound           = errors.New("message not found")
	ErrInvalidChannel            = errors.New("invalid channel")
	ErrInvalidMessageType        = errors.New("invalid message type")
	ErrInvalidRecipient          = errors.New("invalid recipient")
	ErrContentOrTemplateRequired = errors.New("content or template is required")
	ErrMessageAlreadySent        = errors.New("message already sent")
	ErrMessageCannotBeRetried    = errors.New("message cannot be retried")
	ErrChannelNotConfigured      = errors.New("channel not configured")
	ErrChannelDisabled           = errors.New("channel is disabled")
	ErrRateLimitExceeded         = errors.New("rate limit exceeded")
	ErrMessageTooLarge           = errors.New("message content too large")
	ErrInvalidMessageFormat      = errors.New("invalid message format")
)

// Template related errors
var (
	ErrTemplateNotFound      = errors.New("template not found")
	ErrTemplateAlreadyExists = errors.New("template already exists")
	ErrTemplateInactive      = errors.New("template is inactive")
	ErrInvalidTemplate       = errors.New("invalid template")
	ErrTemplateVariableMissing = errors.New("template variable missing")
	ErrTemplateRenderFailed  = errors.New("template render failed")
	ErrTemplateInUse         = errors.New("template is in use")
)

// Channel related errors
var (
	ErrChannelError          = errors.New("channel error")
	ErrSMSError              = errors.New("SMS sending error")
	ErrEmailError            = errors.New("email sending error")
	ErrWhatsAppError         = errors.New("WhatsApp sending error")
	ErrWeChatError           = errors.New("WeChat sending error")
	ErrPushNotificationError = errors.New("push notification error")
	ErrWebhookError          = errors.New("webhook error")
)

// External service errors
var (
	ErrExternalServiceUnavailable = errors.New("external service unavailable")
	ErrExternalServiceTimeout     = errors.New("external service timeout")
	ErrExternalServiceError       = errors.New("external service error")
	ErrInvalidAPIKey              = errors.New("invalid API key")
	ErrInsufficientCredits        = errors.New("insufficient credits")
)

// Error codes for API responses
const (
	ErrorCodeMessageNotFound           = "MESSAGE_NOT_FOUND"
	ErrorCodeInvalidChannel            = "INVALID_CHANNEL"
	ErrorCodeInvalidMessageType        = "INVALID_MESSAGE_TYPE"
	ErrorCodeInvalidRecipient          = "INVALID_RECIPIENT"
	ErrorCodeContentOrTemplateRequired = "CONTENT_OR_TEMPLATE_REQUIRED"
	ErrorCodeMessageAlreadySent        = "MESSAGE_ALREADY_SENT"
	ErrorCodeMessageCannotBeRetried    = "MESSAGE_CANNOT_BE_RETRIED"
	ErrorCodeChannelNotConfigured      = "CHANNEL_NOT_CONFIGURED"
	ErrorCodeChannelDisabled           = "CHANNEL_DISABLED"
	ErrorCodeRateLimitExceeded         = "RATE_LIMIT_EXCEEDED"
	ErrorCodeMessageTooLarge           = "MESSAGE_TOO_LARGE"
	ErrorCodeInvalidMessageFormat      = "INVALID_MESSAGE_FORMAT"
	ErrorCodeTemplateNotFound          = "TEMPLATE_NOT_FOUND"
	ErrorCodeTemplateAlreadyExists     = "TEMPLATE_ALREADY_EXISTS"
	ErrorCodeTemplateInactive          = "TEMPLATE_INACTIVE"
	ErrorCodeInvalidTemplate           = "INVALID_TEMPLATE"
	ErrorCodeTemplateVariableMissing   = "TEMPLATE_VARIABLE_MISSING"
	ErrorCodeTemplateRenderFailed      = "TEMPLATE_RENDER_FAILED"
	ErrorCodeTemplateInUse             = "TEMPLATE_IN_USE"
	ErrorCodeChannelError              = "CHANNEL_ERROR"
	ErrorCodeSMSError                  = "SMS_ERROR"
	ErrorCodeEmailError                = "EMAIL_ERROR"
	ErrorCodeWhatsAppError             = "WHATSAPP_ERROR"
	ErrorCodeWeChatError               = "WECHAT_ERROR"
	ErrorCodePushNotificationError     = "PUSH_NOTIFICATION_ERROR"
	ErrorCodeWebhookError              = "WEBHOOK_ERROR"
	ErrorCodeExternalServiceUnavailable = "EXTERNAL_SERVICE_UNAVAILABLE"
	ErrorCodeExternalServiceTimeout    = "EXTERNAL_SERVICE_TIMEOUT"
	ErrorCodeExternalServiceError      = "EXTERNAL_SERVICE_ERROR"
	ErrorCodeInvalidAPIKey             = "INVALID_API_KEY"
	ErrorCodeInsufficientCredits       = "INSUFFICIENT_CREDITS"
)

// GetErrorCode returns the error code for a given error
func GetErrorCode(err error) string {
	switch err {
	case ErrMessageNotFound:
		return ErrorCodeMessageNotFound
	case ErrInvalidChannel:
		return ErrorCodeInvalidChannel
	case ErrInvalidMessageType:
		return ErrorCodeInvalidMessageType
	case ErrInvalidRecipient:
		return ErrorCodeInvalidRecipient
	case ErrContentOrTemplateRequired:
		return ErrorCodeContentOrTemplateRequired
	case ErrMessageAlreadySent:
		return ErrorCodeMessageAlreadySent
	case ErrMessageCannotBeRetried:
		return ErrorCodeMessageCannotBeRetried
	case ErrChannelNotConfigured:
		return ErrorCodeChannelNotConfigured
	case ErrChannelDisabled:
		return ErrorCodeChannelDisabled
	case ErrRateLimitExceeded:
		return ErrorCodeRateLimitExceeded
	case ErrMessageTooLarge:
		return ErrorCodeMessageTooLarge
	case ErrInvalidMessageFormat:
		return ErrorCodeInvalidMessageFormat
	case ErrTemplateNotFound:
		return ErrorCodeTemplateNotFound
	case ErrTemplateAlreadyExists:
		return ErrorCodeTemplateAlreadyExists
	case ErrTemplateInactive:
		return ErrorCodeTemplateInactive
	case ErrInvalidTemplate:
		return ErrorCodeInvalidTemplate
	case ErrTemplateVariableMissing:
		return ErrorCodeTemplateVariableMissing
	case ErrTemplateRenderFailed:
		return ErrorCodeTemplateRenderFailed
	case ErrTemplateInUse:
		return ErrorCodeTemplateInUse
	case ErrChannelError:
		return ErrorCodeChannelError
	case ErrSMSError:
		return ErrorCodeSMSError
	case ErrEmailError:
		return ErrorCodeEmailError
	case ErrWhatsAppError:
		return ErrorCodeWhatsAppError
	case ErrWeChatError:
		return ErrorCodeWeChatError
	case ErrPushNotificationError:
		return ErrorCodePushNotificationError
	case ErrWebhookError:
		return ErrorCodeWebhookError
	case ErrExternalServiceUnavailable:
		return ErrorCodeExternalServiceUnavailable
	case ErrExternalServiceTimeout:
		return ErrorCodeExternalServiceTimeout
	case ErrExternalServiceError:
		return ErrorCodeExternalServiceError
	case ErrInvalidAPIKey:
		return ErrorCodeInvalidAPIKey
	case ErrInsufficientCredits:
		return ErrorCodeInsufficientCredits
	default:
		return "UNKNOWN_ERROR"
	}
}
