package repository

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"cmhk-platform/internal/communication/model"
)

// MessageRepository defines the interface for message data access
type MessageRepository interface {
	Create(ctx context.Context, message *model.Message) error
	GetByID(ctx context.Context, id uint64) (*model.Message, error)
	Update(ctx context.Context, message *model.Message) error
	Delete(ctx context.Context, id uint64) error
	List(ctx context.Context, query model.MessageListQuery) ([]*model.Message, int64, error)
	UpdateStatus(ctx context.Context, id uint64, status string, errorMessage string) error
	GetPendingMessages(ctx context.Context, limit int) ([]*model.Message, error)
	GetMessagesByStatus(ctx context.Context, status string, limit int) ([]*model.Message, error)
	GetMessageStats(ctx context.Context, tenantID uint64) (*model.MessageStats, error)
	BulkCreate(ctx context.Context, messages []*model.Message) error
}

// messageRepository implements MessageRepository interface
type messageRepository struct {
	db *gorm.DB
}

// NewMessageRepository creates a new message repository
func NewMessageRepository(db *gorm.DB) MessageRepository {
	return &messageRepository{db: db}
}

// Create creates a new message
func (r *messageRepository) Create(ctx context.Context, message *model.Message) error {
	if err := r.db.WithContext(ctx).Create(message).Error; err != nil {
		return fmt.Errorf("failed to create message: %w", err)
	}
	return nil
}

// GetByID retrieves a message by ID
func (r *messageRepository) GetByID(ctx context.Context, id uint64) (*model.Message, error) {
	var message model.Message
	err := r.db.WithContext(ctx).
		Preload("Template").
		First(&message, id).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrMessageNotFound
		}
		return nil, fmt.Errorf("failed to get message by ID: %w", err)
	}
	
	return &message, nil
}

// Update updates a message
func (r *messageRepository) Update(ctx context.Context, message *model.Message) error {
	if err := r.db.WithContext(ctx).Save(message).Error; err != nil {
		return fmt.Errorf("failed to update message: %w", err)
	}
	return nil
}

// Delete soft deletes a message
func (r *messageRepository) Delete(ctx context.Context, id uint64) error {
	if err := r.db.WithContext(ctx).Delete(&model.Message{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete message: %w", err)
	}
	return nil
}

// List retrieves messages with pagination and filtering
func (r *messageRepository) List(ctx context.Context, query model.MessageListQuery) ([]*model.Message, int64, error) {
	var messages []*model.Message
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.Message{})
	
	// Apply filters
	if query.TenantID > 0 {
		db = db.Where("tenant_id = ?", query.TenantID)
	}
	
	if query.Channel != "" {
		db = db.Where("channel = ?", query.Channel)
	}
	
	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}
	
	if query.MessageType != "" {
		db = db.Where("message_type = ?", query.MessageType)
	}
	
	if query.Recipient != "" {
		db = db.Where("recipient LIKE ?", "%"+query.Recipient+"%")
	}
	
	if !query.DateFrom.IsZero() {
		db = db.Where("created_at >= ?", query.DateFrom)
	}
	
	if !query.DateTo.IsZero() {
		db = db.Where("created_at <= ?", query.DateTo)
	}
	
	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count messages: %w", err)
	}
	
	// Apply pagination
	offset := (query.Page - 1) * query.PageSize
	if err := db.Preload("Template").
		Offset(offset).
		Limit(query.PageSize).
		Order("created_at DESC").
		Find(&messages).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list messages: %w", err)
	}
	
	return messages, total, nil
}

// UpdateStatus updates message status
func (r *messageRepository) UpdateStatus(ctx context.Context, id uint64, status string, errorMessage string) error {
	updates := map[string]interface{}{
		"status": status,
	}
	
	if errorMessage != "" {
		updates["error_message"] = errorMessage
	}
	
	if status == model.StatusSent {
		updates["sent_at"] = "NOW()"
	} else if status == model.StatusDelivered {
		updates["delivered_at"] = "NOW()"
	} else if status == model.StatusRead {
		updates["read_at"] = "NOW()"
	}
	
	if err := r.db.WithContext(ctx).
		Model(&model.Message{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update message status: %w", err)
	}
	
	return nil
}

// GetPendingMessages retrieves pending messages
func (r *messageRepository) GetPendingMessages(ctx context.Context, limit int) ([]*model.Message, error) {
	var messages []*model.Message
	
	err := r.db.WithContext(ctx).
		Preload("Template").
		Where("status = ?", model.StatusPending).
		Order("created_at ASC").
		Limit(limit).
		Find(&messages).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get pending messages: %w", err)
	}
	
	return messages, nil
}

// GetMessagesByStatus retrieves messages by status
func (r *messageRepository) GetMessagesByStatus(ctx context.Context, status string, limit int) ([]*model.Message, error) {
	var messages []*model.Message
	
	err := r.db.WithContext(ctx).
		Preload("Template").
		Where("status = ?", status).
		Order("created_at DESC").
		Limit(limit).
		Find(&messages).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get messages by status: %w", err)
	}
	
	return messages, nil
}

// GetMessageStats retrieves message statistics
func (r *messageRepository) GetMessageStats(ctx context.Context, tenantID uint64) (*model.MessageStats, error) {
	stats := &model.MessageStats{
		ChannelStats: make(map[string]int64),
		StatusStats:  make(map[string]int64),
	}
	
	// Get total messages
	if err := r.db.WithContext(ctx).
		Model(&model.Message{}).
		Where("tenant_id = ?", tenantID).
		Count(&stats.TotalMessages).Error; err != nil {
		return nil, fmt.Errorf("failed to get total messages: %w", err)
	}
	
	// Get sent messages
	if err := r.db.WithContext(ctx).
		Model(&model.Message{}).
		Where("tenant_id = ? AND status IN (?)", tenantID, []string{model.StatusSent, model.StatusDelivered, model.StatusRead}).
		Count(&stats.SentMessages).Error; err != nil {
		return nil, fmt.Errorf("failed to get sent messages: %w", err)
	}
	
	// Get delivered messages
	if err := r.db.WithContext(ctx).
		Model(&model.Message{}).
		Where("tenant_id = ? AND status IN (?)", tenantID, []string{model.StatusDelivered, model.StatusRead}).
		Count(&stats.DeliveredMessages).Error; err != nil {
		return nil, fmt.Errorf("failed to get delivered messages: %w", err)
	}
	
	// Get failed messages
	if err := r.db.WithContext(ctx).
		Model(&model.Message{}).
		Where("tenant_id = ? AND status = ?", tenantID, model.StatusFailed).
		Count(&stats.FailedMessages).Error; err != nil {
		return nil, fmt.Errorf("failed to get failed messages: %w", err)
	}
	
	// Get channel stats
	var channelResults []struct {
		Channel string
		Count   int64
	}
	if err := r.db.WithContext(ctx).
		Model(&model.Message{}).
		Select("channel, COUNT(*) as count").
		Where("tenant_id = ?", tenantID).
		Group("channel").
		Scan(&channelResults).Error; err != nil {
		return nil, fmt.Errorf("failed to get channel stats: %w", err)
	}
	
	for _, result := range channelResults {
		stats.ChannelStats[result.Channel] = result.Count
	}
	
	// Get status stats
	var statusResults []struct {
		Status string
		Count  int64
	}
	if err := r.db.WithContext(ctx).
		Model(&model.Message{}).
		Select("status, COUNT(*) as count").
		Where("tenant_id = ?", tenantID).
		Group("status").
		Scan(&statusResults).Error; err != nil {
		return nil, fmt.Errorf("failed to get status stats: %w", err)
	}
	
	for _, result := range statusResults {
		stats.StatusStats[result.Status] = result.Count
	}
	
	return stats, nil
}

// BulkCreate creates multiple messages in a single transaction
func (r *messageRepository) BulkCreate(ctx context.Context, messages []*model.Message) error {
	if len(messages) == 0 {
		return nil
	}
	
	if err := r.db.WithContext(ctx).CreateInBatches(messages, 100).Error; err != nil {
		return fmt.Errorf("failed to bulk create messages: %w", err)
	}
	
	return nil
}
