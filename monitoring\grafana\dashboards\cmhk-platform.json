{"dashboard": {"id": null, "title": "CMHK Communication Platform", "tags": ["cmhk", "platform", "communication"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Overview", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "up{job=\"cmhk-platform\"}", "legendFormat": "Service Status"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}}, {"id": 2, "title": "HTTP Request Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "rate(http_requests_total{job=\"cmhk-platform\"}[5m])", "legendFormat": "{{method}} {{path}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}]}, {"id": 3, "title": "HTTP Response Time", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"cmhk-platform\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"cmhk-platform\"}[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"label": "Seconds", "min": 0}]}, {"id": 4, "title": "Error Rate", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "rate(http_requests_total{job=\"cmhk-platform\",status=~\"4..|5..\"}[5m])", "legendFormat": "{{status}} errors"}], "yAxes": [{"label": "Errors/sec", "min": 0}]}, {"id": 5, "title": "Database Connections", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "database_connections_active{job=\"cmhk-platform\"}", "legendFormat": "Active connections"}, {"expr": "database_connections_idle{job=\"cmhk-platform\"}", "legendFormat": "Idle connections"}], "yAxes": [{"label": "Connections", "min": 0}]}, {"id": 6, "title": "Message Processing", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "rate(messages_sent_total{job=\"cmhk-platform\"}[5m])", "legendFormat": "{{channel}} sent"}, {"expr": "rate(messages_failed_total{job=\"cmhk-platform\"}[5m])", "legendFormat": "{{channel}} failed"}], "yAxes": [{"label": "Messages/sec", "min": 0}]}, {"id": 7, "title": "<PERSON><PERSON>", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "rate(cache_hits_total{job=\"cmhk-platform\"}[5m])", "legendFormat": "Cache hits"}, {"expr": "rate(cache_misses_total{job=\"cmhk-platform\"}[5m])", "legendFormat": "<PERSON><PERSON> misses"}], "yAxes": [{"label": "Operations/sec", "min": 0}]}, {"id": 8, "title": "Memory Usage", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "process_resident_memory_bytes{job=\"cmhk-platform\"}", "legendFormat": "Resident memory"}, {"expr": "go_memstats_heap_inuse_bytes{job=\"cmhk-platform\"}", "legendFormat": "Heap in use"}], "yAxes": [{"label": "Bytes", "min": 0}]}, {"id": 9, "title": "Goroutines", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "go_goroutines{job=\"cmhk-platform\"}", "legendFormat": "Goroutines"}], "yAxes": [{"label": "Count", "min": 0}]}, {"id": 10, "title": "Top API Endpoints", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}, "targets": [{"expr": "topk(10, sum by (path) (rate(http_requests_total{job=\"cmhk-platform\"}[5m])))", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"path": "Endpoint", "Value": "Requests/sec"}}}]}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up{job=\"cmhk-platform\"}, instance)", "refresh": 1, "includeAll": true, "allValue": ".*"}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(process_start_time_seconds{job=\"cmhk-platform\"}[1m]) > 0", "titleFormat": "Deployment", "textFormat": "Service restarted"}]}}}