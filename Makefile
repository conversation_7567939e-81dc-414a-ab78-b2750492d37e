# CMHK Communication Platform Makefile

.PHONY: build run test clean deps fmt lint docker-build docker-run

# Variables
APP_NAME=cmhk-platform
BUILD_DIR=bin
DOCKER_IMAGE=cmhk-platform:latest

# Default target
all: deps fmt lint test build

# Install dependencies
deps:
	@echo "Installing dependencies..."
	go mod download
	go mod tidy

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Lint code
lint:
	@echo "Linting code..."
	golangci-lint run

# Run tests
test:
	@echo "Running tests..."
	go test -v -race -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Build application
build:
	@echo "Building application..."
	mkdir -p $(BUILD_DIR)
	go build -o $(BUILD_DIR)/$(APP_NAME) ./cmd/server

# Run application
run:
	@echo "Running application..."
	go run ./cmd/server

# Clean build artifacts
clean:
	@echo "Cleaning..."
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# Generate swagger docs
swagger:
	@echo "Generating swagger docs..."
	swag init -g cmd/server/main.go

# Docker build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .

# Docker run
docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 $(DOCKER_IMAGE)

# Development setup
dev-setup: deps
	@echo "Setting up development environment..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/swaggo/swag/cmd/swag@latest

# Database migration (placeholder)
migrate:
	@echo "Running database migrations..."
	# TODO: Implement database migration

# Help
help:
	@echo "Available targets:"
	@echo "  deps        - Install dependencies"
	@echo "  fmt         - Format code"
	@echo "  lint        - Lint code"
	@echo "  test        - Run tests"
	@echo "  build       - Build application"
	@echo "  run         - Run application"
	@echo "  clean       - Clean build artifacts"
	@echo "  swagger     - Generate swagger docs"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run  - Run Docker container"
	@echo "  dev-setup   - Setup development environment"
	@echo "  migrate     - Run database migrations"
	@echo "  help        - Show this help"
