#!/bin/bash

# CMHK Communication Platform Test Script

set -e

echo "Running tests for CMHK Communication Platform..."

# Configuration
COVERAGE_FILE="coverage.out"
COVERAGE_HTML="coverage.html"
TEST_TIMEOUT="10m"

# Clean previous test results
echo "Cleaning previous test results..."
rm -f $COVERAGE_FILE $COVERAGE_HTML

# Run unit tests with coverage
echo "Running unit tests..."
go test -v -race -timeout=$TEST_TIMEOUT -coverprofile=$COVERAGE_FILE ./...

# Check if tests passed
if [ $? -eq 0 ]; then
    echo "✅ All tests passed!"
else
    echo "❌ Some tests failed!"
    exit 1
fi

# Generate coverage report
if [ -f $COVERAGE_FILE ]; then
    echo "Generating coverage report..."
    go tool cover -html=$COVERAGE_FILE -o $COVERAGE_HTML
    
    # Display coverage summary
    echo "Coverage summary:"
    go tool cover -func=$COVERAGE_FILE | tail -1
    
    echo "HTML coverage report generated: $COVERAGE_HTML"
fi

# Run integration tests if they exist
if [ -d "tests/integration" ]; then
    echo "Running integration tests..."
    go test -v -tags=integration ./tests/integration/...
fi

# Run benchmark tests
echo "Running benchmark tests..."
go test -bench=. -benchmem ./... || true

# Check for race conditions
echo "Checking for race conditions..."
go test -race ./... > /dev/null 2>&1 && echo "✅ No race conditions detected" || echo "⚠️  Race conditions detected"

# Lint code
if command -v golangci-lint &> /dev/null; then
    echo "Running linter..."
    golangci-lint run
    echo "✅ Linting completed"
else
    echo "⚠️  golangci-lint not found, skipping linting"
fi

# Security check
if command -v gosec &> /dev/null; then
    echo "Running security check..."
    gosec ./...
    echo "✅ Security check completed"
else
    echo "⚠️  gosec not found, skipping security check"
fi

echo "All tests completed successfully! 🎉"
