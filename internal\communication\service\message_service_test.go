package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"cmhk-platform/internal/communication/model"
	"cmhk-platform/pkg/logger"
)

// MockMessageRepository is a mock implementation of MessageRepository
type MockMessageRepository struct {
	mock.Mock
}

func (m *MockMessageRepository) Create(ctx context.Context, message *model.Message) error {
	args := m.Called(ctx, message)
	return args.Error(0)
}

func (m *MockMessageRepository) GetByID(ctx context.Context, id uint64) (*model.Message, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Message), args.Error(1)
}

func (m *MockMessageRepository) Update(ctx context.Context, message *model.Message) error {
	args := m.Called(ctx, message)
	return args.Error(0)
}

func (m *MockMessageRepository) Delete(ctx context.Context, id uint64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockMessageRepository) List(ctx context.Context, query model.MessageListQuery) ([]*model.Message, int64, error) {
	args := m.Called(ctx, query)
	return args.Get(0).([]*model.Message), args.Get(1).(int64), args.Error(2)
}

func (m *MockMessageRepository) UpdateStatus(ctx context.Context, id uint64, status string, errorMessage string) error {
	args := m.Called(ctx, id, status, errorMessage)
	return args.Error(0)
}

func (m *MockMessageRepository) GetPendingMessages(ctx context.Context, limit int) ([]*model.Message, error) {
	args := m.Called(ctx, limit)
	return args.Get(0).([]*model.Message), args.Error(1)
}

func (m *MockMessageRepository) GetMessagesByStatus(ctx context.Context, status string, limit int) ([]*model.Message, error) {
	args := m.Called(ctx, status, limit)
	return args.Get(0).([]*model.Message), args.Error(1)
}

func (m *MockMessageRepository) GetMessageStats(ctx context.Context, tenantID uint64) (*model.MessageStats, error) {
	args := m.Called(ctx, tenantID)
	return args.Get(0).(*model.MessageStats), args.Error(1)
}

func (m *MockMessageRepository) BulkCreate(ctx context.Context, messages []*model.Message) error {
	args := m.Called(ctx, messages)
	return args.Error(0)
}

// MockTemplateRepository is a mock implementation of TemplateRepository
type MockTemplateRepository struct {
	mock.Mock
}

func (m *MockTemplateRepository) Create(ctx context.Context, template *model.Template) error {
	args := m.Called(ctx, template)
	return args.Error(0)
}

func (m *MockTemplateRepository) GetByID(ctx context.Context, id uint64) (*model.Template, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Template), args.Error(1)
}

func (m *MockTemplateRepository) GetByCode(ctx context.Context, tenantID uint64, code string) (*model.Template, error) {
	args := m.Called(ctx, tenantID, code)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Template), args.Error(1)
}

func (m *MockTemplateRepository) Update(ctx context.Context, template *model.Template) error {
	args := m.Called(ctx, template)
	return args.Error(0)
}

func (m *MockTemplateRepository) Delete(ctx context.Context, id uint64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTemplateRepository) List(ctx context.Context, query model.TemplateListQuery) ([]*model.Template, int64, error) {
	args := m.Called(ctx, query)
	return args.Get(0).([]*model.Template), args.Get(1).(int64), args.Error(2)
}

func (m *MockTemplateRepository) IsInUse(ctx context.Context, templateID uint64) (bool, error) {
	args := m.Called(ctx, templateID)
	return args.Bool(0), args.Error(1)
}

// MockChannelService is a mock implementation of ChannelService
type MockChannelService struct {
	mock.Mock
}

func (m *MockChannelService) SendMessage(ctx context.Context, message *model.Message) error {
	args := m.Called(ctx, message)
	return args.Error(0)
}

func (m *MockChannelService) IsChannelEnabled(channel string) bool {
	args := m.Called(channel)
	return args.Bool(0)
}

func (m *MockChannelService) GetChannelConfig(channel string) (*model.ChannelConfig, error) {
	args := m.Called(channel)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.ChannelConfig), args.Error(1)
}

// Test setup helper
func setupMessageServiceTest() (*messageService, *MockMessageRepository, *MockTemplateRepository, *MockChannelService) {
	mockMessageRepo := &MockMessageRepository{}
	mockTemplateRepo := &MockTemplateRepository{}
	mockChannelSvc := &MockChannelService{}
	
	logger := logger.NewLogger(&logger.Config{Level: "info"})
	
	service := &messageService{
		messageRepo:  mockMessageRepo,
		templateRepo: mockTemplateRepo,
		channelSvc:   mockChannelSvc,
		logger:       logger,
	}
	
	return service, mockMessageRepo, mockTemplateRepo, mockChannelSvc
}

func TestMessageService_SendMessage(t *testing.T) {
	service, mockMessageRepo, mockTemplateRepo, mockChannelSvc := setupMessageServiceTest()
	ctx := context.Background()

	t.Run("successful message sending without template", func(t *testing.T) {
		req := model.SendMessageRequest{
			MessageType: model.MessageTypeNotification,
			Channel:     model.ChannelSMS,
			Recipient:   "+1234567890",
			Subject:     "Test Subject",
			Content:     "Test message content",
			TenantID:    1,
		}

		mockMessageRepo.On("Create", ctx, mock.AnythingOfType("*model.Message")).Return(nil)

		message, err := service.SendMessage(ctx, req)

		assert.NoError(t, err)
		assert.NotNil(t, message)
		assert.Equal(t, model.MessageTypeNotification, message.MessageType)
		assert.Equal(t, model.ChannelSMS, message.Channel)
		assert.Equal(t, "+1234567890", message.Recipient)
		assert.Equal(t, "Test message content", message.Content)
		assert.Equal(t, model.StatusPending, message.Status)
		mockMessageRepo.AssertExpectations(t)
	})

	t.Run("successful message sending with template", func(t *testing.T) {
		templateID := uint64(1)
		req := model.SendMessageRequest{
			MessageType: model.MessageTypeNotification,
			Channel:     model.ChannelEmail,
			Recipient:   "<EMAIL>",
			TemplateID:  &templateID,
			Variables:   map[string]string{"name": "John", "code": "123456"},
			TenantID:    1,
		}

		template := &model.Template{
			ID:      1,
			Subject: "Welcome {{name}}",
			Content: "Your verification code is: {{code}}",
			Status:  model.TemplateStatusActive,
		}

		mockTemplateRepo.On("GetByID", ctx, uint64(1)).Return(template, nil)
		mockMessageRepo.On("Create", ctx, mock.AnythingOfType("*model.Message")).Return(nil)

		message, err := service.SendMessage(ctx, req)

		assert.NoError(t, err)
		assert.NotNil(t, message)
		assert.Equal(t, "Welcome John", message.Subject)
		assert.Equal(t, "Your verification code is: 123456", message.Content)
		mockTemplateRepo.AssertExpectations(t)
		mockMessageRepo.AssertExpectations(t)
	})

	t.Run("invalid channel", func(t *testing.T) {
		req := model.SendMessageRequest{
			MessageType: model.MessageTypeNotification,
			Channel:     "invalid_channel",
			Recipient:   "+1234567890",
			Content:     "Test message",
			TenantID:    1,
		}

		message, err := service.SendMessage(ctx, req)

		assert.Error(t, err)
		assert.Equal(t, model.ErrInvalidChannel, err)
		assert.Nil(t, message)
	})

	t.Run("template not found", func(t *testing.T) {
		templateID := uint64(999)
		req := model.SendMessageRequest{
			MessageType: model.MessageTypeNotification,
			Channel:     model.ChannelSMS,
			Recipient:   "+1234567890",
			TemplateID:  &templateID,
			TenantID:    1,
		}

		mockTemplateRepo.On("GetByID", ctx, uint64(999)).Return(nil, model.ErrTemplateNotFound)

		message, err := service.SendMessage(ctx, req)

		assert.Error(t, err)
		assert.Equal(t, model.ErrTemplateNotFound, err)
		assert.Nil(t, message)
		mockTemplateRepo.AssertExpectations(t)
	})

	t.Run("inactive template", func(t *testing.T) {
		templateID := uint64(1)
		req := model.SendMessageRequest{
			MessageType: model.MessageTypeNotification,
			Channel:     model.ChannelSMS,
			Recipient:   "+1234567890",
			TemplateID:  &templateID,
			TenantID:    1,
		}

		template := &model.Template{
			ID:      1,
			Content: "Test template",
			Status:  model.TemplateStatusInactive,
		}

		mockTemplateRepo.On("GetByID", ctx, uint64(1)).Return(template, nil)

		message, err := service.SendMessage(ctx, req)

		assert.Error(t, err)
		assert.Equal(t, model.ErrTemplateInactive, err)
		assert.Nil(t, message)
		mockTemplateRepo.AssertExpectations(t)
	})
}

func TestMessageService_SendBulkMessage(t *testing.T) {
	service, mockMessageRepo, _, _ := setupMessageServiceTest()
	ctx := context.Background()

	t.Run("successful bulk message sending", func(t *testing.T) {
		req := model.SendBulkMessageRequest{
			MessageType: model.MessageTypeMarketing,
			Channel:     model.ChannelSMS,
			Content:     "Bulk message content",
			Recipients: []model.BulkMessageRecipient{
				{Recipient: "+1234567890", Variables: map[string]string{"name": "John"}},
				{Recipient: "+0987654321", Variables: map[string]string{"name": "Jane"}},
			},
			TenantID: 1,
		}

		mockMessageRepo.On("BulkCreate", ctx, mock.AnythingOfType("[]*model.Message")).Return(nil)

		messages, err := service.SendBulkMessage(ctx, req)

		assert.NoError(t, err)
		assert.NotNil(t, messages)
		assert.Len(t, messages, 2)
		assert.Equal(t, "+1234567890", messages[0].Recipient)
		assert.Equal(t, "+0987654321", messages[1].Recipient)
		mockMessageRepo.AssertExpectations(t)
	})

	t.Run("invalid channel for bulk message", func(t *testing.T) {
		req := model.SendBulkMessageRequest{
			MessageType: model.MessageTypeMarketing,
			Channel:     "invalid_channel",
			Content:     "Bulk message content",
			Recipients: []model.BulkMessageRecipient{
				{Recipient: "+1234567890"},
			},
			TenantID: 1,
		}

		messages, err := service.SendBulkMessage(ctx, req)

		assert.Error(t, err)
		assert.Equal(t, model.ErrInvalidChannel, err)
		assert.Nil(t, messages)
	})
}

func TestMessageService_GetMessageStats(t *testing.T) {
	service, mockMessageRepo, _, _ := setupMessageServiceTest()
	ctx := context.Background()

	t.Run("successful stats retrieval", func(t *testing.T) {
		expectedStats := &model.MessageStats{
			TotalMessages:     100,
			SentMessages:      80,
			DeliveredMessages: 75,
			FailedMessages:    5,
			ChannelStats: map[string]int64{
				model.ChannelSMS:   50,
				model.ChannelEmail: 50,
			},
			StatusStats: map[string]int64{
				model.StatusSent:      80,
				model.StatusDelivered: 75,
				model.StatusFailed:    5,
			},
		}

		mockMessageRepo.On("GetMessageStats", ctx, uint64(1)).Return(expectedStats, nil)

		stats, err := service.GetMessageStats(ctx, 1)

		assert.NoError(t, err)
		assert.NotNil(t, stats)
		assert.Equal(t, int64(100), stats.TotalMessages)
		assert.Equal(t, int64(80), stats.SentMessages)
		assert.Equal(t, int64(75), stats.DeliveredMessages)
		assert.Equal(t, int64(5), stats.FailedMessages)
		mockMessageRepo.AssertExpectations(t)
	})
}
