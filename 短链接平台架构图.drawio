<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="企业级短链接管理平台架构图（优化版）" id="architecture-optimized">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1800" pageHeight="1400" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 用户层 -->
        <mxCell id="users-group" value="👥 用户层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=1;fontSize=14;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="50" width="1600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="enterprise-user" value="企业用户&lt;br&gt;营销团队" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=0;" vertex="1" parent="users-group">
          <mxGeometry x="200" y="40" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="developer" value="开发者&lt;br&gt;API用户" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=0;" vertex="1" parent="users-group">
          <mxGeometry x="740" y="40" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="end-user" value="最终用户&lt;br&gt;链接访问者" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=0;" vertex="1" parent="users-group">
          <mxGeometry x="1280" y="40" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- 客户端层 -->
        <mxCell id="client-group" value="💻 客户端层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=1;fontSize=14;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="180" width="1600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="web-console" value="Web管理后台&lt;br&gt;• 链接管理 • 数据分析&lt;br&gt;• 团队管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=0;" vertex="1" parent="client-group">
          <mxGeometry x="170" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="api-interface" value="RESTful API&lt;br&gt;• 链接CRUD • 批量操作&lt;br&gt;• 数据查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=0;" vertex="1" parent="client-group">
          <mxGeometry x="710" y="40" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="mobile-app" value="移动应用&lt;br&gt;• 浏览器插件&lt;br&gt;• 移动端管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=0;" vertex="1" parent="client-group">
          <mxGeometry x="1250" y="40" width="180" height="50" as="geometry" />
        </mxCell>

        <!-- API网关层 -->
        <mxCell id="gateway-group" value="🚪 API网关层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontStyle=1;fontSize=14;startSize=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="310" width="1600" height="80" as="geometry" />
        </mxCell>
        <mxCell id="api-gateway" value="API Gateway • 身份认证 • 流量控制 • 请求路由 • 速率限制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontStyle=0;" vertex="1" parent="gateway-group">
          <mxGeometry x="500" y="35" width="600" height="40" as="geometry" />
        </mxCell>

        <!-- 应用服务层 -->
        <mxCell id="services-group" value="⚙️ 应用服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="480" width="1500" height="280" as="geometry" />
        </mxCell>
        
        <!-- 核心服务 -->
        <mxCell id="core-services" value="核心服务" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#1b5e20;" vertex="1" parent="services-group">
          <mxGeometry x="50" y="40" width="300" height="220" as="geometry" />
        </mxCell>
        <mxCell id="id-service" value="ID生成服务&lt;br&gt;• Snowflake算法&lt;br&gt;• Base62编码&lt;br&gt;• 全局唯一ID" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#1b5e20;" vertex="1" parent="core-services">
          <mxGeometry x="20" y="40" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="redirect-service" value="重定向服务&lt;br&gt;• 302跳转&lt;br&gt;• 高并发处理&lt;br&gt;• 点击追踪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#1b5e20;" vertex="1" parent="core-services">
          <mxGeometry x="160" y="40" width="120" height="70" as="geometry" />
        </mxCell>

        <!-- 业务服务 -->
        <mxCell id="business-services" value="业务服务" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="services-group">
          <mxGeometry x="400" y="40" width="500" height="220" as="geometry" />
        </mxCell>
        <mxCell id="mgmt-service" value="管理服务&lt;br&gt;• 链接管理&lt;br&gt;• 用户管理&lt;br&gt;• 权限控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="business-services">
          <mxGeometry x="20" y="40" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="analytics-service" value="分析服务&lt;br&gt;• 点击统计&lt;br&gt;• 地理分析&lt;br&gt;• 设备识别" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="business-services">
          <mxGeometry x="190" y="40" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="qr-service" value="二维码服务&lt;br&gt;• 静态二维码&lt;br&gt;• 活码生成&lt;br&gt;• 渠道活码" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#f57f17;" vertex="1" parent="business-services">
          <mxGeometry x="360" y="40" width="120" height="70" as="geometry" />
        </mxCell>

        <!-- 高级服务 -->
        <mxCell id="advanced-services" value="高级服务" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="services-group">
          <mxGeometry x="950" y="40" width="500" height="220" as="geometry" />
        </mxCell>
        <mxCell id="ab-service" value="A/B测试服务&lt;br&gt;• 流量分配&lt;br&gt;• 效果对比&lt;br&gt;• 统计分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="advanced-services">
          <mxGeometry x="20" y="40" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="risk-service" value="风控服务&lt;br&gt;• 内容审核&lt;br&gt;• 行为监控&lt;br&gt;• 安全检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="advanced-services">
          <mxGeometry x="190" y="40" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="notify-service" value="通知服务&lt;br&gt;• 邮件通知&lt;br&gt;• 短信通知&lt;br&gt;• Webhook" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="advanced-services">
          <mxGeometry x="360" y="40" width="120" height="70" as="geometry" />
        </mxCell>

        <!-- 缓存层 -->
        <mxCell id="cache-group" value="🚀 缓存层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00695c;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="790" width="1500" height="100" as="geometry" />
        </mxCell>
        <mxCell id="redis-cluster" value="Redis集群&lt;br&gt;• 热点链接缓存 • 会话存储&lt;br&gt;• 计数器 • 分布式锁" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00695c;" vertex="1" parent="cache-group">
          <mxGeometry x="600" y="30" width="300" height="60" as="geometry" />
        </mxCell>

        <!-- 数据存储层 -->
        <mxCell id="storage-group" value="💾 数据存储层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="920" width="1500" height="120" as="geometry" />
        </mxCell>
        <mxCell id="nosql-db" value="NoSQL数据库&lt;br&gt;MongoDB/DynamoDB&lt;br&gt;• URL映射存储&lt;br&gt;• 点击事件日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="storage-group">
          <mxGeometry x="200" y="40" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="rdbms" value="关系型数据库&lt;br&gt;PostgreSQL/MySQL&lt;br&gt;• 用户信息&lt;br&gt;• 权限配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="storage-group">
          <mxGeometry x="660" y="40" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="file-storage" value="文件存储&lt;br&gt;• 二维码图片&lt;br&gt;• 导出文件&lt;br&gt;• 日志文件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="storage-group">
          <mxGeometry x="1120" y="40" width="180" height="70" as="geometry" />
        </mxCell>

        <!-- 外部服务层 -->
        <mxCell id="external-group" value="🌐 外部服务层" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#424242;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="1070" width="1500" height="120" as="geometry" />
        </mxCell>
        <mxCell id="security-api" value="安全检测API&lt;br&gt;• Google Safe Browsing&lt;br&gt;• 恶意链接检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#424242;" vertex="1" parent="external-group">
          <mxGeometry x="100" y="40" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="geo-api" value="地理位置API&lt;br&gt;• IP地理定位&lt;br&gt;• 城市级精度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#424242;" vertex="1" parent="external-group">
          <mxGeometry x="460" y="40" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="sms-service" value="短信服务&lt;br&gt;• 验证码发送&lt;br&gt;• 营销短信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#424242;" vertex="1" parent="external-group">
          <mxGeometry x="820" y="40" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="monitor-service" value="监控服务&lt;br&gt;• 系统监控&lt;br&gt;• 日志收集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#424242;" vertex="1" parent="external-group">
          <mxGeometry x="1180" y="40" width="180" height="70" as="geometry" />
        </mxCell>

        <!-- 连接线 -->
        <mxCell id="conn1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="enterprise-user" target="web-console">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="developer" target="api-interface">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="end-user" target="redirect-service">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="web-console" target="api-gateway">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="api-interface" target="api-gateway">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="mobile-app" target="api-gateway">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
