package middleware

import (
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"cmhk-platform/internal/shared/types"
	"cmhk-platform/pkg/logger"
)

// ValidationConfig holds validation configuration
type ValidationConfig struct {
	// SQL injection patterns
	EnableSQLInjectionCheck bool     `yaml:"enable_sql_injection_check" mapstructure:"enable_sql_injection_check"`
	SQLInjectionPatterns    []string `yaml:"sql_injection_patterns" mapstructure:"sql_injection_patterns"`
	
	// XSS patterns
	EnableXSSCheck bool     `yaml:"enable_xss_check" mapstructure:"enable_xss_check"`
	XSSPatterns    []string `yaml:"xss_patterns" mapstructure:"xss_patterns"`
	
	// Path traversal patterns
	EnablePathTraversalCheck bool     `yaml:"enable_path_traversal_check" mapstructure:"enable_path_traversal_check"`
	PathTraversalPatterns    []string `yaml:"path_traversal_patterns" mapstructure:"path_traversal_patterns"`
	
	// Command injection patterns
	EnableCommandInjectionCheck bool     `yaml:"enable_command_injection_check" mapstructure:"enable_command_injection_check"`
	CommandInjectionPatterns    []string `yaml:"command_injection_patterns" mapstructure:"command_injection_patterns"`
	
	// Content validation
	MaxRequestSize    int64    `yaml:"max_request_size" mapstructure:"max_request_size"`
	AllowedMimeTypes  []string `yaml:"allowed_mime_types" mapstructure:"allowed_mime_types"`
	BlockedExtensions []string `yaml:"blocked_extensions" mapstructure:"blocked_extensions"`
	
	// Header validation
	RequiredHeaders   []string `yaml:"required_headers" mapstructure:"required_headers"`
	BlockedUserAgents []string `yaml:"blocked_user_agents" mapstructure:"blocked_user_agents"`
	
	// Custom validation rules
	CustomPatterns map[string]string `yaml:"custom_patterns" mapstructure:"custom_patterns"`
}

// SecurityValidator implements input validation and security checks
type SecurityValidator struct {
	config           *ValidationConfig
	logger           *logger.Logger
	sqlPatterns      []*regexp.Regexp
	xssPatterns      []*regexp.Regexp
	pathPatterns     []*regexp.Regexp
	commandPatterns  []*regexp.Regexp
	customPatterns   map[string]*regexp.Regexp
}

// NewSecurityValidator creates a new security validator
func NewSecurityValidator(config *ValidationConfig, logger *logger.Logger) *SecurityValidator {
	if config == nil {
		config = DefaultValidationConfig()
	}
	
	validator := &SecurityValidator{
		config:         config,
		logger:         logger,
		customPatterns: make(map[string]*regexp.Regexp),
	}
	
	// Compile regex patterns
	validator.compilePatterns()
	
	return validator
}

// DefaultValidationConfig returns default validation configuration
func DefaultValidationConfig() *ValidationConfig {
	return &ValidationConfig{
		EnableSQLInjectionCheck: true,
		SQLInjectionPatterns: []string{
			`(?i)(union\s+select)`,
			`(?i)(select\s+.*\s+from)`,
			`(?i)(insert\s+into)`,
			`(?i)(delete\s+from)`,
			`(?i)(update\s+.*\s+set)`,
			`(?i)(drop\s+table)`,
			`(?i)(create\s+table)`,
			`(?i)(alter\s+table)`,
			`(?i)(exec\s*\()`,
			`(?i)(script\s*:)`,
			`(?i)(javascript\s*:)`,
			`(?i)(vbscript\s*:)`,
			`(?i)(onload\s*=)`,
			`(?i)(onerror\s*=)`,
			`(?i)(onclick\s*=)`,
			`(?i)(\'\s*or\s+\'\d+\'\s*=\s*\'\d+)`,
			`(?i)(\'\s*or\s+\d+\s*=\s*\d+)`,
			`(?i)(--\s)`,
			`(?i)(/\*.*\*/)`,
		},
		EnableXSSCheck: true,
		XSSPatterns: []string{
			`(?i)<script[^>]*>.*?</script>`,
			`(?i)<iframe[^>]*>.*?</iframe>`,
			`(?i)<object[^>]*>.*?</object>`,
			`(?i)<embed[^>]*>`,
			`(?i)<link[^>]*>`,
			`(?i)<meta[^>]*>`,
			`(?i)javascript:`,
			`(?i)vbscript:`,
			`(?i)data:text/html`,
			`(?i)on\w+\s*=`,
		},
		EnablePathTraversalCheck: true,
		PathTraversalPatterns: []string{
			`\.\.\/`,
			`\.\.\\`,
			`%2e%2e%2f`,
			`%2e%2e%5c`,
			`%252e%252e%252f`,
			`%252e%252e%255c`,
		},
		EnableCommandInjectionCheck: true,
		CommandInjectionPatterns: []string{
			`(?i)(;|\||&|&&|\|\||>|<|>>|<<|\$\(|\`|\$\{)`,
			`(?i)(nc\s|netcat\s|wget\s|curl\s|ping\s|nslookup\s|dig\s)`,
			`(?i)(rm\s|del\s|format\s|fdisk\s)`,
			`(?i)(cat\s|type\s|more\s|less\s|head\s|tail\s)`,
		},
		MaxRequestSize:   10 * 1024 * 1024, // 10MB
		AllowedMimeTypes: []string{
			"application/json",
			"application/x-www-form-urlencoded",
			"multipart/form-data",
			"text/plain",
		},
		BlockedExtensions: []string{
			".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js",
			".jar", ".php", ".asp", ".aspx", ".jsp", ".sh", ".ps1",
		},
		RequiredHeaders: []string{
			"User-Agent",
		},
		BlockedUserAgents: []string{
			"sqlmap",
			"nikto",
			"nmap",
			"masscan",
			"zap",
			"burp",
		},
		CustomPatterns: make(map[string]string),
	}
}

// compilePatterns compiles all regex patterns
func (sv *SecurityValidator) compilePatterns() {
	// Compile SQL injection patterns
	for _, pattern := range sv.config.SQLInjectionPatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			sv.sqlPatterns = append(sv.sqlPatterns, regex)
		} else {
			sv.logger.Warn("Failed to compile SQL injection pattern", zap.String("pattern", pattern), zap.Error(err))
		}
	}
	
	// Compile XSS patterns
	for _, pattern := range sv.config.XSSPatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			sv.xssPatterns = append(sv.xssPatterns, regex)
		} else {
			sv.logger.Warn("Failed to compile XSS pattern", zap.String("pattern", pattern), zap.Error(err))
		}
	}
	
	// Compile path traversal patterns
	for _, pattern := range sv.config.PathTraversalPatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			sv.pathPatterns = append(sv.pathPatterns, regex)
		} else {
			sv.logger.Warn("Failed to compile path traversal pattern", zap.String("pattern", pattern), zap.Error(err))
		}
	}
	
	// Compile command injection patterns
	for _, pattern := range sv.config.CommandInjectionPatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			sv.commandPatterns = append(sv.commandPatterns, regex)
		} else {
			sv.logger.Warn("Failed to compile command injection pattern", zap.String("pattern", pattern), zap.Error(err))
		}
	}
	
	// Compile custom patterns
	for name, pattern := range sv.config.CustomPatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			sv.customPatterns[name] = regex
		} else {
			sv.logger.Warn("Failed to compile custom pattern", zap.String("name", name), zap.String("pattern", pattern), zap.Error(err))
		}
	}
}

// Middleware returns a Gin middleware for input validation
func (sv *SecurityValidator) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check request size
		if sv.config.MaxRequestSize > 0 && c.Request.ContentLength > sv.config.MaxRequestSize {
			sv.logger.Warn("Request size too large", 
				zap.Int64("size", c.Request.ContentLength),
				zap.Int64("max_size", sv.config.MaxRequestSize))
			types.ErrorResponse(c, http.StatusRequestEntityTooLarge, types.ErrorCodeValidation, "Request size too large")
			c.Abort()
			return
		}
		
		// Check required headers
		for _, header := range sv.config.RequiredHeaders {
			if c.GetHeader(header) == "" {
				sv.logger.Warn("Missing required header", zap.String("header", header))
				types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Missing required header: "+header)
				c.Abort()
				return
			}
		}
		
		// Check blocked user agents
		userAgent := c.GetHeader("User-Agent")
		for _, blocked := range sv.config.BlockedUserAgents {
			if strings.Contains(strings.ToLower(userAgent), strings.ToLower(blocked)) {
				sv.logger.Warn("Blocked user agent", zap.String("user_agent", userAgent))
				types.ErrorResponse(c, http.StatusForbidden, types.ErrorCodeForbidden, "Access denied")
				c.Abort()
				return
			}
		}
		
		// Validate URL path
		if threat := sv.validateInput(c.Request.URL.Path); threat != "" {
			sv.logger.Warn("Security threat detected in URL path", 
				zap.String("path", c.Request.URL.Path),
				zap.String("threat", threat))
			types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid request")
			c.Abort()
			return
		}
		
		// Validate query parameters
		for key, values := range c.Request.URL.Query() {
			for _, value := range values {
				if threat := sv.validateInput(value); threat != "" {
					sv.logger.Warn("Security threat detected in query parameter", 
						zap.String("key", key),
						zap.String("value", value),
						zap.String("threat", threat))
					types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid request")
					c.Abort()
					return
				}
			}
		}
		
		// Validate headers
		for key, values := range c.Request.Header {
			for _, value := range values {
				if threat := sv.validateInput(value); threat != "" {
					sv.logger.Warn("Security threat detected in header", 
						zap.String("key", key),
						zap.String("value", value),
						zap.String("threat", threat))
					types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid request")
					c.Abort()
					return
				}
			}
		}
		
		c.Next()
	}
}

// validateInput checks input against all security patterns
func (sv *SecurityValidator) validateInput(input string) string {
	// Check SQL injection
	if sv.config.EnableSQLInjectionCheck {
		for _, pattern := range sv.sqlPatterns {
			if pattern.MatchString(input) {
				return "sql_injection"
			}
		}
	}
	
	// Check XSS
	if sv.config.EnableXSSCheck {
		for _, pattern := range sv.xssPatterns {
			if pattern.MatchString(input) {
				return "xss"
			}
		}
	}
	
	// Check path traversal
	if sv.config.EnablePathTraversalCheck {
		for _, pattern := range sv.pathPatterns {
			if pattern.MatchString(input) {
				return "path_traversal"
			}
		}
	}
	
	// Check command injection
	if sv.config.EnableCommandInjectionCheck {
		for _, pattern := range sv.commandPatterns {
			if pattern.MatchString(input) {
				return "command_injection"
			}
		}
	}
	
	// Check custom patterns
	for name, pattern := range sv.customPatterns {
		if pattern.MatchString(input) {
			return name
		}
	}
	
	return ""
}

// ValidateFileUpload validates file uploads
func (sv *SecurityValidator) ValidateFileUpload(filename, contentType string) error {
	// Check file extension
	for _, ext := range sv.config.BlockedExtensions {
		if strings.HasSuffix(strings.ToLower(filename), ext) {
			return fmt.Errorf("file extension not allowed: %s", ext)
		}
	}
	
	// Check MIME type
	if len(sv.config.AllowedMimeTypes) > 0 {
		allowed := false
		for _, mimeType := range sv.config.AllowedMimeTypes {
			if contentType == mimeType {
				allowed = true
				break
			}
		}
		if !allowed {
			return fmt.Errorf("MIME type not allowed: %s", contentType)
		}
	}
	
	return nil
}
