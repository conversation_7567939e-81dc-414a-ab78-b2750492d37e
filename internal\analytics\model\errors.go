package model

import "errors"

// Report related errors
var (
	ErrReportNotFound       = errors.New("report not found")
	ErrReportAlreadyExists  = errors.New("report already exists")
	ErrReportNotExecutable  = errors.New("report is not executable")
	ErrInvalidReportType    = errors.New("invalid report type")
	ErrInvalidDataSource    = errors.New("invalid data source")
	ErrInvalidQuery         = errors.New("invalid query")
	ErrQueryExecutionFailed = errors.New("query execution failed")
	ErrReportInUse          = errors.New("report is in use")
)

// Execution related errors
var (
	ErrExecutionNotFound    = errors.New("report execution not found")
	ErrExecutionRunning     = errors.New("execution is already running")
	ErrExecutionFailed      = errors.New("execution failed")
	ErrInvalidParameters    = errors.New("invalid parameters")
	ErrResultNotFound       = errors.New("execution result not found")
	ErrResultExpired        = errors.New("execution result has expired")
)

// Metric related errors
var (
	ErrMetricNotFound     = errors.New("metric not found")
	ErrInvalidMetricType  = errors.New("invalid metric type")
	ErrInvalidMetricValue = errors.New("invalid metric value")
	ErrInvalidTimeRange   = errors.New("invalid time range")
	ErrMetricDataNotFound = errors.New("metric data not found")
)

// Dashboard related errors
var (
	ErrDashboardNotFound      = errors.New("dashboard not found")
	ErrDashboardAlreadyExists = errors.New("dashboard already exists")
	ErrInvalidLayout          = errors.New("invalid dashboard layout")
	ErrInvalidWidget          = errors.New("invalid dashboard widget")
	ErrDashboardNotPublic     = errors.New("dashboard is not public")
)

// Alert related errors
var (
	ErrAlertNotFound       = errors.New("alert not found")
	ErrAlertAlreadyExists  = errors.New("alert already exists")
	ErrInvalidCondition    = errors.New("invalid alert condition")
	ErrInvalidThreshold    = errors.New("invalid alert threshold")
	ErrInvalidSeverity     = errors.New("invalid alert severity")
	ErrInvalidAction       = errors.New("invalid alert action")
	ErrAlertNotTriggerable = errors.New("alert is not triggerable")
)

// Error codes for API responses
const (
	ErrorCodeReportNotFound       = "REPORT_NOT_FOUND"
	ErrorCodeReportAlreadyExists  = "REPORT_ALREADY_EXISTS"
	ErrorCodeReportNotExecutable  = "REPORT_NOT_EXECUTABLE"
	ErrorCodeInvalidReportType    = "INVALID_REPORT_TYPE"
	ErrorCodeInvalidDataSource    = "INVALID_DATA_SOURCE"
	ErrorCodeInvalidQuery         = "INVALID_QUERY"
	ErrorCodeQueryExecutionFailed = "QUERY_EXECUTION_FAILED"
	ErrorCodeReportInUse          = "REPORT_IN_USE"
	ErrorCodeExecutionNotFound    = "EXECUTION_NOT_FOUND"
	ErrorCodeExecutionRunning     = "EXECUTION_RUNNING"
	ErrorCodeExecutionFailed      = "EXECUTION_FAILED"
	ErrorCodeInvalidParameters    = "INVALID_PARAMETERS"
	ErrorCodeResultNotFound       = "RESULT_NOT_FOUND"
	ErrorCodeResultExpired        = "RESULT_EXPIRED"
	ErrorCodeMetricNotFound       = "METRIC_NOT_FOUND"
	ErrorCodeInvalidMetricType    = "INVALID_METRIC_TYPE"
	ErrorCodeInvalidMetricValue   = "INVALID_METRIC_VALUE"
	ErrorCodeInvalidTimeRange     = "INVALID_TIME_RANGE"
	ErrorCodeMetricDataNotFound   = "METRIC_DATA_NOT_FOUND"
	ErrorCodeDashboardNotFound    = "DASHBOARD_NOT_FOUND"
	ErrorCodeDashboardAlreadyExists = "DASHBOARD_ALREADY_EXISTS"
	ErrorCodeInvalidLayout        = "INVALID_LAYOUT"
	ErrorCodeInvalidWidget        = "INVALID_WIDGET"
	ErrorCodeDashboardNotPublic   = "DASHBOARD_NOT_PUBLIC"
	ErrorCodeAlertNotFound        = "ALERT_NOT_FOUND"
	ErrorCodeAlertAlreadyExists   = "ALERT_ALREADY_EXISTS"
	ErrorCodeInvalidCondition     = "INVALID_CONDITION"
	ErrorCodeInvalidThreshold     = "INVALID_THRESHOLD"
	ErrorCodeInvalidSeverity      = "INVALID_SEVERITY"
	ErrorCodeInvalidAction        = "INVALID_ACTION"
	ErrorCodeAlertNotTriggerable  = "ALERT_NOT_TRIGGERABLE"
)

// GetErrorCode returns the error code for a given error
func GetErrorCode(err error) string {
	switch err {
	case ErrReportNotFound:
		return ErrorCodeReportNotFound
	case ErrReportAlreadyExists:
		return ErrorCodeReportAlreadyExists
	case ErrReportNotExecutable:
		return ErrorCodeReportNotExecutable
	case ErrInvalidReportType:
		return ErrorCodeInvalidReportType
	case ErrInvalidDataSource:
		return ErrorCodeInvalidDataSource
	case ErrInvalidQuery:
		return ErrorCodeInvalidQuery
	case ErrQueryExecutionFailed:
		return ErrorCodeQueryExecutionFailed
	case ErrReportInUse:
		return ErrorCodeReportInUse
	case ErrExecutionNotFound:
		return ErrorCodeExecutionNotFound
	case ErrExecutionRunning:
		return ErrorCodeExecutionRunning
	case ErrExecutionFailed:
		return ErrorCodeExecutionFailed
	case ErrInvalidParameters:
		return ErrorCodeInvalidParameters
	case ErrResultNotFound:
		return ErrorCodeResultNotFound
	case ErrResultExpired:
		return ErrorCodeResultExpired
	case ErrMetricNotFound:
		return ErrorCodeMetricNotFound
	case ErrInvalidMetricType:
		return ErrorCodeInvalidMetricType
	case ErrInvalidMetricValue:
		return ErrorCodeInvalidMetricValue
	case ErrInvalidTimeRange:
		return ErrorCodeInvalidTimeRange
	case ErrMetricDataNotFound:
		return ErrorCodeMetricDataNotFound
	case ErrDashboardNotFound:
		return ErrorCodeDashboardNotFound
	case ErrDashboardAlreadyExists:
		return ErrorCodeDashboardAlreadyExists
	case ErrInvalidLayout:
		return ErrorCodeInvalidLayout
	case ErrInvalidWidget:
		return ErrorCodeInvalidWidget
	case ErrDashboardNotPublic:
		return ErrorCodeDashboardNotPublic
	case ErrAlertNotFound:
		return ErrorCodeAlertNotFound
	case ErrAlertAlreadyExists:
		return ErrorCodeAlertAlreadyExists
	case ErrInvalidCondition:
		return ErrorCodeInvalidCondition
	case ErrInvalidThreshold:
		return ErrorCodeInvalidThreshold
	case ErrInvalidSeverity:
		return ErrorCodeInvalidSeverity
	case ErrInvalidAction:
		return ErrorCodeInvalidAction
	case ErrAlertNotTriggerable:
		return ErrorCodeAlertNotTriggerable
	default:
		return "UNKNOWN_ERROR"
	}
}
