# CMHK通信能力开放平台

## 项目概述

CMHK通信能力开放平台是为中国移动香港（CMHK）打造的统一通信和客户数据管理平台，旨在提供全渠道通信能力、客户数据洞察和智能营销服务。

## 🏗️ 系统架构

### 微服务架构（优化版）
- **用户服务** (user-service) - 统一用户管理和认证授权
- **通信服务** (communication-service) - 多渠道消息处理和模板管理
- **客户服务** (customer-service) - 客户数据管理和画像分析
- **营销服务** (marketing-service) - 营销活动和短链接管理
- **分析服务** (analytics-service) - 数据分析和报表生成
- **平台服务** (platform-service) - 系统管理和支撑功能
- **文件服务** (file-service) - 文件存储和处理
- **工作流服务** (workflow-service) - 业务流程编排

### 技术栈
- **框架**: Spring Boot 3.2.x + Spring Cloud 2023.0.x
- **数据库**: MySQL 8.0 + Redis 7.0 + ClickHouse
- **服务治理**: Nacos + Sentinel
- **消息队列**: Apache Kafka
- **监控**: Prometheus + Grafana + SkyWalking
- **容器化**: Docker + Kubernetes

## 📁 项目结构

```
cmhk-platform/
├── cmhk-common/              # 公共模块
│   ├── cmhk-common-core/     # 核心工具类
│   ├── cmhk-common-security/ # 安全组件
│   ├── cmhk-common-redis/    # Redis组件
│   └── cmhk-common-web/      # Web组件
├── cmhk-gateway/             # API网关服务
├── cmhk-user-service/        # 用户管理服务
├── cmhk-communication-service/ # 通信服务
├── cmhk-customer-service/    # 客户服务（后续开发）
├── cmhk-marketing-service/   # 营销服务（后续开发）
├── cmhk-analytics-service/   # 分析服务（后续开发）
├── cmhk-platform-service/    # 平台服务（后续开发）
├── cmhk-file-service/        # 文件服务（后续开发）
├── cmhk-workflow-service/    # 工作流服务（后续开发）
├── docker-compose.yml       # 开发环境配置
├── pom.xml                  # 父POM配置
└── README.md                # 项目说明
```

## 🚀 快速开始

### 环境要求
- JDK 17+
- Maven 3.8+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 7.0+

### 本地开发环境搭建

1. **克隆项目**
```bash
git clone <repository-url>
cd cmhk-platform
```

2. **启动基础设施**
```bash
docker-compose up -d mysql redis nacos
```

3. **编译项目**
```bash
mvn clean compile
```

4. **启动服务**
```bash
# 启动网关服务
cd cmhk-gateway && mvn spring-boot:run

# 启动用户服务
cd cmhk-user-service && mvn spring-boot:run

# 启动通信服务
cd cmhk-communication-service && mvn spring-boot:run
```

### 服务端口分配
- **API网关**: 8080
- **用户服务**: 8090
- **通信服务**: 8091
- **Nacos控制台**: 8848
- **MySQL**: 3306
- **Redis**: 6379

## 📖 API文档

启动服务后，可通过以下地址访问API文档：
- **网关聚合文档**: http://localhost:8080/doc.html
- **用户服务文档**: http://localhost:8090/doc.html
- **通信服务文档**: http://localhost:8091/doc.html

## 🧪 测试

### 运行单元测试
```bash
mvn test
```

### 运行集成测试
```bash
mvn verify
```

### 测试覆盖率报告
```bash
mvn jacoco:report
```

## 📦 构建和部署

### 构建Docker镜像
```bash
mvn clean package dockerfile:build
```

### 部署到Kubernetes
```bash
kubectl apply -f k8s/
```

## 🔧 配置说明

### 环境配置
- **开发环境**: application-dev.yml
- **测试环境**: application-test.yml
- **生产环境**: application-prod.yml

### 数据库配置
```yaml
spring:
  datasource:
    url: **************************************************************************************************************
    username: ${DB_USERNAME:cmhk}
    password: ${DB_PASSWORD:cmhk123}
```

### Redis配置
```yaml
spring:
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
```

## 📊 监控和运维

### 健康检查
- **网关服务**: http://localhost:8080/actuator/health
- **用户服务**: http://localhost:8090/actuator/health
- **通信服务**: http://localhost:8091/actuator/health

### 指标监控
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📝 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 必须编写单元测试，覆盖率不低于80%

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 📄 许可证

本项目为CMHK内部项目，版权所有。

## 📞 联系我们

- **项目团队**: <EMAIL>
- **技术支持**: <EMAIL>

---

*最后更新时间: 2024年1月*
