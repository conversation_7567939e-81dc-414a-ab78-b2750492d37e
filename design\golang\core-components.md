# Golang核心组件实现示例

## 1. 应用程序入口 (cmd/server/main.go)

```go
package main

import (
    "context"
    "fmt"
    "log"
    "net/http"
    "os"
    "os/signal"
    "syscall"
    "time"

    "github.com/gin-gonic/gin"
    "go.uber.org/zap"

    "cmhk-platform/internal/api"
    "cmhk-platform/internal/shared/config"
    "cmhk-platform/internal/shared/database"
    "cmhk-platform/pkg/logger"
)

func main() {
    // 初始化日志
    log := logger.NewLogger()
    defer log.Sync()

    // 加载配置
    cfg, err := config.Load()
    if err != nil {
        log.Fatal("Failed to load config", zap.Error(err))
    }

    // 初始化数据库连接
    db, err := database.NewMySQLConnection(cfg.Database.MySQL)
    if err != nil {
        log.Fatal("Failed to connect to MySQL", zap.Error(err))
    }
    defer db.Close()

    redisClient, err := database.NewRedisConnection(cfg.Database.Redis)
    if err != nil {
        log.Fatal("Failed to connect to Redis", zap.Error(err))
    }
    defer redisClient.Close()

    clickhouseDB, err := database.NewClickHouseConnection(cfg.Database.ClickHouse)
    if err != nil {
        log.Fatal("Failed to connect to ClickHouse", zap.Error(err))
    }
    defer clickhouseDB.Close()

    // 初始化应用依赖
    deps := &api.Dependencies{
        Logger:      log,
        Config:      cfg,
        MySQL:       db,
        Redis:       redisClient,
        ClickHouse:  clickhouseDB,
    }

    // 设置Gin模式
    if cfg.Server.Mode == "production" {
        gin.SetMode(gin.ReleaseMode)
    }

    // 初始化路由
    router := api.NewRouter(deps)

    // 创建HTTP服务器
    server := &http.Server{
        Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
        Handler:      router,
        ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
        WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
    }

    // 启动服务器
    go func() {
        log.Info("Starting server", zap.Int("port", cfg.Server.Port))
        if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
            log.Fatal("Failed to start server", zap.Error(err))
        }
    }()

    // 优雅关闭
    quit := make(chan os.Signal, 1)
    signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
    <-quit

    log.Info("Shutting down server...")

    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    if err := server.Shutdown(ctx); err != nil {
        log.Fatal("Server forced to shutdown", zap.Error(err))
    }

    log.Info("Server exited")
}
```

## 2. 配置管理 (internal/shared/config/config.go)

```go
package config

import (
    "fmt"
    "strings"

    "github.com/spf13/viper"
)

type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    Cache    CacheConfig    `mapstructure:"cache"`
    Queue    QueueConfig    `mapstructure:"queue"`
    Auth     AuthConfig     `mapstructure:"auth"`
    External ExternalConfig `mapstructure:"external"`
}

type ServerConfig struct {
    Port         int    `mapstructure:"port"`
    Mode         string `mapstructure:"mode"`
    ReadTimeout  int    `mapstructure:"read_timeout"`
    WriteTimeout int    `mapstructure:"write_timeout"`
}

type DatabaseConfig struct {
    MySQL      MySQLConfig      `mapstructure:"mysql"`
    Redis      RedisConfig      `mapstructure:"redis"`
    ClickHouse ClickHouseConfig `mapstructure:"clickhouse"`
}

type MySQLConfig struct {
    Host         string `mapstructure:"host"`
    Port         int    `mapstructure:"port"`
    Username     string `mapstructure:"username"`
    Password     string `mapstructure:"password"`
    Database     string `mapstructure:"database"`
    MaxOpenConns int    `mapstructure:"max_open_conns"`
    MaxIdleConns int    `mapstructure:"max_idle_conns"`
    MaxLifetime  int    `mapstructure:"max_lifetime"`
}

type RedisConfig struct {
    Addresses []string `mapstructure:"addresses"`
    Password  string   `mapstructure:"password"`
    DB        int      `mapstructure:"db"`
    PoolSize  int      `mapstructure:"pool_size"`
}

type ClickHouseConfig struct {
    Host     string `mapstructure:"host"`
    Port     int    `mapstructure:"port"`
    Username string `mapstructure:"username"`
    Password string `mapstructure:"password"`
    Database string `mapstructure:"database"`
}

type CacheConfig struct {
    DefaultTTL int `mapstructure:"default_ttl"`
}

type QueueConfig struct {
    Kafka KafkaConfig `mapstructure:"kafka"`
}

type KafkaConfig struct {
    Brokers []string `mapstructure:"brokers"`
    GroupID string   `mapstructure:"group_id"`
}

type AuthConfig struct {
    JWTSecret     string `mapstructure:"jwt_secret"`
    TokenExpiry   int    `mapstructure:"token_expiry"`
    RefreshExpiry int    `mapstructure:"refresh_expiry"`
}

type ExternalConfig struct {
    SMS      SMSConfig      `mapstructure:"sms"`
    WhatsApp WhatsAppConfig `mapstructure:"whatsapp"`
    WeChat   WeChatConfig   `mapstructure:"wechat"`
}

type SMSConfig struct {
    Gateway string `mapstructure:"gateway"`
    APIKey  string `mapstructure:"api_key"`
    Secret  string `mapstructure:"secret"`
}

type WhatsAppConfig struct {
    BusinessAPIURL string `mapstructure:"business_api_url"`
    AccessToken    string `mapstructure:"access_token"`
    PhoneNumberID  string `mapstructure:"phone_number_id"`
}

type WeChatConfig struct {
    AppID     string `mapstructure:"app_id"`
    AppSecret string `mapstructure:"app_secret"`
}

func Load() (*Config, error) {
    viper.SetConfigName("config")
    viper.SetConfigType("yaml")
    viper.AddConfigPath("./configs")
    viper.AddConfigPath(".")

    // 环境变量支持
    viper.SetEnvPrefix("CMHK")
    viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
    viper.AutomaticEnv()

    // 设置默认值
    setDefaults()

    if err := viper.ReadInConfig(); err != nil {
        return nil, fmt.Errorf("failed to read config file: %w", err)
    }

    var config Config
    if err := viper.Unmarshal(&config); err != nil {
        return nil, fmt.Errorf("failed to unmarshal config: %w", err)
    }

    return &config, nil
}

func setDefaults() {
    viper.SetDefault("server.port", 8080)
    viper.SetDefault("server.mode", "development")
    viper.SetDefault("server.read_timeout", 30)
    viper.SetDefault("server.write_timeout", 30)
    
    viper.SetDefault("database.mysql.port", 3306)
    viper.SetDefault("database.mysql.max_open_conns", 100)
    viper.SetDefault("database.mysql.max_idle_conns", 10)
    viper.SetDefault("database.mysql.max_lifetime", 3600)
    
    viper.SetDefault("database.redis.db", 0)
    viper.SetDefault("database.redis.pool_size", 100)
    
    viper.SetDefault("cache.default_ttl", 3600)
    
    viper.SetDefault("auth.token_expiry", 3600)
    viper.SetDefault("auth.refresh_expiry", 86400)
}
```

## 3. 数据库连接 (internal/shared/database/mysql.go)

```go
package database

import (
    "fmt"
    "time"

    "gorm.io/driver/mysql"
    "gorm.io/gorm"
    "gorm.io/gorm/logger"

    "cmhk-platform/internal/shared/config"
)

func NewMySQLConnection(cfg config.MySQLConfig) (*gorm.DB, error) {
    dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
        cfg.Username,
        cfg.Password,
        cfg.Host,
        cfg.Port,
        cfg.Database,
    )

    db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    })
    if err != nil {
        return nil, fmt.Errorf("failed to connect to MySQL: %w", err)
    }

    sqlDB, err := db.DB()
    if err != nil {
        return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
    }

    // 设置连接池参数
    sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
    sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
    sqlDB.SetConnMaxLifetime(time.Duration(cfg.MaxLifetime) * time.Second)

    // 测试连接
    if err := sqlDB.Ping(); err != nil {
        return nil, fmt.Errorf("failed to ping MySQL: %w", err)
    }

    return db, nil
}

// Redis连接 (internal/shared/database/redis.go)
package database

import (
    "context"
    "fmt"

    "github.com/redis/go-redis/v9"

    "cmhk-platform/internal/shared/config"
)

func NewRedisConnection(cfg config.RedisConfig) (*redis.ClusterClient, error) {
    client := redis.NewClusterClient(&redis.ClusterOptions{
        Addrs:    cfg.Addresses,
        Password: cfg.Password,
        PoolSize: cfg.PoolSize,
    })

    // 测试连接
    ctx := context.Background()
    if err := client.Ping(ctx).Err(); err != nil {
        return nil, fmt.Errorf("failed to ping Redis: %w", err)
    }

    return client, nil
}

## 4. 用户模块示例

### 用户模型 (internal/user/model/user.go)

```go
package model

import (
    "time"
    "gorm.io/gorm"
)

type User struct {
    ID               uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
    Username         string         `gorm:"uniqueIndex;size:50;not null" json:"username"`
    Email            string         `gorm:"uniqueIndex;size:100" json:"email"`
    Phone            string         `gorm:"uniqueIndex;size:20" json:"phone"`
    PasswordHash     string         `gorm:"size:255;not null" json:"-"`
    Salt             string         `gorm:"size:32;not null" json:"-"`
    Status           int8           `gorm:"default:1" json:"status"`
    TenantID         uint64         `gorm:"not null;index:idx_tenant_status" json:"tenant_id"`
    LastLoginAt      *time.Time     `gorm:"index" json:"last_login_at"`
    FailedLoginCount int            `gorm:"default:0" json:"failed_login_count"`
    LockedUntil      *time.Time     `json:"locked_until"`
    CreatedAt        time.Time      `json:"created_at"`
    UpdatedAt        time.Time      `json:"updated_at"`
    DeletedAt        gorm.DeletedAt `gorm:"index" json:"-"`

    // 关联关系
    Roles []Role `gorm:"many2many:user_roles;" json:"roles,omitempty"`
}

type Role struct {
    ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
    TenantID    uint64         `gorm:"not null;uniqueIndex:uk_tenant_code" json:"tenant_id"`
    RoleCode    string         `gorm:"size:50;not null;uniqueIndex:uk_tenant_code" json:"role_code"`
    RoleName    string         `gorm:"size:100;not null" json:"role_name"`
    Permissions string         `gorm:"type:json;not null" json:"permissions"`
    Status      int8           `gorm:"default:1" json:"status"`
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

type UserRole struct {
    ID        uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
    UserID    uint64     `gorm:"not null;uniqueIndex:uk_user_role" json:"user_id"`
    RoleID    uint64     `gorm:"not null;uniqueIndex:uk_user_role" json:"role_id"`
    GrantedBy uint64     `json:"granted_by"`
    GrantedAt time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"granted_at"`
    ExpiresAt *time.Time `json:"expires_at"`
}

// DTO结构
type CreateUserRequest struct {
    Username string `json:"username" binding:"required,min=3,max=50"`
    Email    string `json:"email" binding:"required,email"`
    Phone    string `json:"phone" binding:"required"`
    Password string `json:"password" binding:"required,min=8"`
    TenantID uint64 `json:"tenant_id" binding:"required"`
}

type UpdateUserRequest struct {
    Email  string `json:"email" binding:"omitempty,email"`
    Phone  string `json:"phone"`
    Status int8   `json:"status" binding:"omitempty,oneof=0 1"`
}

type LoginRequest struct {
    Username string `json:"username" binding:"required"`
    Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
    AccessToken  string    `json:"access_token"`
    RefreshToken string    `json:"refresh_token"`
    ExpiresAt    time.Time `json:"expires_at"`
    User         UserInfo  `json:"user"`
}

type UserInfo struct {
    ID       uint64   `json:"id"`
    Username string   `json:"username"`
    Email    string   `json:"email"`
    Phone    string   `json:"phone"`
    Status   int8     `json:"status"`
    TenantID uint64   `json:"tenant_id"`
    Roles    []string `json:"roles"`
}
```

### 用户仓储接口 (internal/user/repository/user_repository.go)

```go
package repository

import (
    "context"
    "cmhk-platform/internal/user/model"
)

type UserRepository interface {
    Create(ctx context.Context, user *model.User) error
    GetByID(ctx context.Context, id uint64) (*model.User, error)
    GetByUsername(ctx context.Context, username string) (*model.User, error)
    GetByEmail(ctx context.Context, email string) (*model.User, error)
    Update(ctx context.Context, user *model.User) error
    Delete(ctx context.Context, id uint64) error
    List(ctx context.Context, tenantID uint64, offset, limit int) ([]*model.User, int64, error)
    AssignRole(ctx context.Context, userID, roleID uint64) error
    RemoveRole(ctx context.Context, userID, roleID uint64) error
    GetUserRoles(ctx context.Context, userID uint64) ([]*model.Role, error)
}

type RoleRepository interface {
    Create(ctx context.Context, role *model.Role) error
    GetByID(ctx context.Context, id uint64) (*model.Role, error)
    GetByCode(ctx context.Context, tenantID uint64, code string) (*model.Role, error)
    Update(ctx context.Context, role *model.Role) error
    Delete(ctx context.Context, id uint64) error
    List(ctx context.Context, tenantID uint64) ([]*model.Role, error)
}
```
