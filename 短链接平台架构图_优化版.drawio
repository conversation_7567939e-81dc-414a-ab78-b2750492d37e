<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.2.0">
  <diagram name="企业级短链接管理平台架构图" id="gDEkk9ZJy6Wz_A6DWLtc">
    <mxGraphModel dx="2037" dy="1061" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-199" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="50" width="1600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-200" value="用户层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1976d2;strokeColor=#1976d2;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="50" width="30" height="100" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-201" value="企业用户&lt;br&gt;营销团队" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="300" y="75" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-202" value="开发者&lt;br&gt;API用户" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="840" y="75" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-203" value="最终用户&lt;br&gt;链接访问者" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1380" y="75" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-204" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="180" width="1600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-205" value="&amp;nbsp;客户端层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7b1fa2;strokeColor=#7b1fa2;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="180" width="30" height="100" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-206" value="Web管理后台&lt;br&gt;• 链接管理 • 数据分析&lt;br&gt;• 团队管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="270" y="205" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-207" value="RESTful API&lt;br&gt;• 链接CRUD • 批量操作&lt;br&gt;• 数据查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="810" y="205" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-208" value="移动应用&lt;br&gt;• 浏览器插件&lt;br&gt;• 移动端管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1350" y="205" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-209" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="310" width="1600" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-210" value="API网关层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f57c00;strokeColor=#f57c00;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="310" width="30" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-211" value="API Gateway • 身份认证 • 流量控制 • 请求路由 • 速率限制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="600" y="330" width="600" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-212" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="420" width="1600" height="240" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-213" value="&amp;nbsp;应用服务层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6c757d;strokeColor=#6c757d;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="420" width="30" height="240" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-214" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;strokeWidth=1;opacity=40;" vertex="1" parent="1">
          <mxGeometry x="150" y="440" width="1500" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-215" value="核心服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#388e3c;strokeColor=#388e3c;fontColor=#ffffff;fontStyle=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="160" y="445" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-216" value="ID生成服务&lt;br&gt;Snowflake • Base62" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="350" y="450" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-217" value="重定向服务&lt;br&gt;302跳转 • 高并发" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1300" y="450" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-218" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#fbc02d;strokeWidth=1;opacity=40;" vertex="1" parent="1">
          <mxGeometry x="150" y="510" width="1500" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-219" value="业务服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fbc02d;strokeColor=#fbc02d;fontColor=#ffffff;fontStyle=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="160" y="515" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-220" value="管理服务&lt;br&gt;链接 • 用户 • 权限" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#fbc02d;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="350" y="520" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-221" value="分析服务&lt;br&gt;统计 • 地理 • 设备" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#fbc02d;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="825" y="520" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-222" value="二维码服务&lt;br&gt;静态码 • 活码" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#fbc02d;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1300" y="520" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-223" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;strokeWidth=1;opacity=40;" vertex="1" parent="1">
          <mxGeometry x="150" y="580" width="1500" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-224" value="高级服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c2185b;strokeColor=#c2185b;fontColor=#ffffff;fontStyle=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="160" y="585" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-225" value="A/B测试&lt;br&gt;流量分配 • 效果对比" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="350" y="590" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-226" value="风控服务&lt;br&gt;内容审核 • 行为监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="825" y="590" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-227" value="通知服务&lt;br&gt;邮件 • 短信 • Webhook" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1300" y="590" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-228" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00796b;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="690" width="1600" height="120" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-229" value="数据层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#00796b;strokeColor=#00796b;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="690" width="30" height="120" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-230" value="Redis集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00796b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="220" y="720" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-231" value="Mysql数据库集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00796b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="620" y="720" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-232" value="RabbitMQ队列集群" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00796b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1020" y="720" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-239" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="830" width="1600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-240" value="&amp;nbsp;外部服务层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#616161;strokeColor=#616161;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="100" y="830" width="30" height="100" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-241" value="安全检测API&lt;br&gt;• Safe Browsing&lt;br&gt;• 恶意链接检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="225" y="850" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-242" value="地理位置API&lt;br&gt;• IP地理定位&lt;br&gt;• 城市级精度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="625" y="850" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-243" value="短信服务&lt;br&gt;• 验证码发送&lt;br&gt;• 营销短信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1025" y="850" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-244" value="监控服务&lt;br&gt;• 系统监控&lt;br&gt;• 日志收集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1425" y="850" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-245" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#1976d2;" edge="1" parent="1" source="cHv76ppeaY5MQ6a3qNE9-201">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="360" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-246" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#1976d2;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="cHv76ppeaY5MQ6a3qNE9-202" target="cHv76ppeaY5MQ6a3qNE9-204">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-247" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#1976d2;" edge="1" parent="1" source="cHv76ppeaY5MQ6a3qNE9-203" target="cHv76ppeaY5MQ6a3qNE9-217">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1540" y="160" />
              <mxPoint x="1540" y="400" />
              <mxPoint x="1475" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cHv76ppeaY5MQ6a3qNE9-258" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#388e3c;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="cHv76ppeaY5MQ6a3qNE9-217" target="cHv76ppeaY5MQ6a3qNE9-221">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="900" y="470" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
