package repository

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"cmhk-platform/internal/communication/model"
)

// TemplateRepository defines the interface for template data access
type TemplateRepository interface {
	Create(ctx context.Context, template *model.Template) error
	GetByID(ctx context.Context, id uint64) (*model.Template, error)
	GetByCode(ctx context.Context, tenantID uint64, code string) (*model.Template, error)
	Update(ctx context.Context, template *model.Template) error
	Delete(ctx context.Context, id uint64) error
	List(ctx context.Context, query model.TemplateListQuery) ([]*model.Template, int64, error)
	IsInUse(ctx context.Context, templateID uint64) (bool, error)
}

// templateRepository implements TemplateRepository interface
type templateRepository struct {
	db *gorm.DB
}

// NewTemplateRepository creates a new template repository
func NewTemplateRepository(db *gorm.DB) TemplateRepository {
	return &templateRepository{db: db}
}

// Create creates a new template
func (r *templateRepository) Create(ctx context.Context, template *model.Template) error {
	if err := r.db.WithContext(ctx).Create(template).Error; err != nil {
		return fmt.Errorf("failed to create template: %w", err)
	}
	return nil
}

// GetByID retrieves a template by ID
func (r *templateRepository) GetByID(ctx context.Context, id uint64) (*model.Template, error) {
	var template model.Template
	err := r.db.WithContext(ctx).First(&template, id).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrTemplateNotFound
		}
		return nil, fmt.Errorf("failed to get template by ID: %w", err)
	}
	
	return &template, nil
}

// GetByCode retrieves a template by tenant ID and template code
func (r *templateRepository) GetByCode(ctx context.Context, tenantID uint64, code string) (*model.Template, error) {
	var template model.Template
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND template_code = ?", tenantID, code).
		First(&template).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrTemplateNotFound
		}
		return nil, fmt.Errorf("failed to get template by code: %w", err)
	}
	
	return &template, nil
}

// Update updates a template
func (r *templateRepository) Update(ctx context.Context, template *model.Template) error {
	if err := r.db.WithContext(ctx).Save(template).Error; err != nil {
		return fmt.Errorf("failed to update template: %w", err)
	}
	return nil
}

// Delete soft deletes a template
func (r *templateRepository) Delete(ctx context.Context, id uint64) error {
	if err := r.db.WithContext(ctx).Delete(&model.Template{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}
	return nil
}

// List retrieves templates with pagination and filtering
func (r *templateRepository) List(ctx context.Context, query model.TemplateListQuery) ([]*model.Template, int64, error) {
	var templates []*model.Template
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.Template{})
	
	// Apply filters
	if query.TenantID > 0 {
		db = db.Where("tenant_id = ?", query.TenantID)
	}
	
	if query.Channel != "" {
		db = db.Where("channel = ?", query.Channel)
	}
	
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}
	
	if query.Search != "" {
		searchPattern := "%" + query.Search + "%"
		db = db.Where("template_name LIKE ? OR template_code LIKE ?", 
			searchPattern, searchPattern)
	}
	
	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count templates: %w", err)
	}
	
	// Apply pagination
	offset := (query.Page - 1) * query.PageSize
	if err := db.Offset(offset).
		Limit(query.PageSize).
		Order("created_at DESC").
		Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list templates: %w", err)
	}
	
	return templates, total, nil
}

// IsInUse checks if a template is being used by any messages
func (r *templateRepository) IsInUse(ctx context.Context, templateID uint64) (bool, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&model.Message{}).
		Where("template_id = ?", templateID).
		Count(&count).Error
	
	if err != nil {
		return false, fmt.Errorf("failed to check template usage: %w", err)
	}
	
	return count > 0, nil
}
