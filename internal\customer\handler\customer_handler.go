package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"cmhk-platform/internal/customer/model"
	"cmhk-platform/internal/customer/service"
	"cmhk-platform/internal/shared/auth"
	"cmhk-platform/internal/shared/types"
	"cmhk-platform/pkg/logger"
)

// CustomerHandler handles customer-related HTTP requests
type CustomerHandler struct {
	customerService service.CustomerService
	logger          *logger.Logger
}

// NewCustomerHandler creates a new customer handler
func NewCustomerHandler(customerService service.CustomerService, logger *logger.Logger) *CustomerHandler {
	return &CustomerHandler{
		customerService: customerService,
		logger:          logger,
	}
}

// CreateCustomer handles customer creation requests
func (h *CustomerHandler) CreateCustomer(c *gin.Context) {
	var req model.CreateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	// Get current tenant ID from context
	if tenantID, ok := auth.GetCurrentTenantID(c); ok {
		req.TenantID = tenantID
	}

	customer, err := h.customerService.CreateCustomer(c.Request.Context(), req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Customer created successfully", customer.ToCustomerResponse())
}

// GetCustomer handles get customer by ID requests
func (h *CustomerHandler) GetCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid customer ID")
		return
	}

	customer, err := h.customerService.GetCustomer(c.Request.Context(), id)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponse(c, customer.ToCustomerResponse())
}

// UpdateCustomer handles customer update requests
func (h *CustomerHandler) UpdateCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid customer ID")
		return
	}

	var req model.UpdateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	customer, err := h.customerService.UpdateCustomer(c.Request.Context(), id, req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Customer updated successfully", customer.ToCustomerResponse())
}

// DeleteCustomer handles customer deletion requests
func (h *CustomerHandler) DeleteCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid customer ID")
		return
	}

	if err := h.customerService.DeleteCustomer(c.Request.Context(), id); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Customer deleted successfully", nil)
}

// ListCustomers handles customer listing requests
func (h *CustomerHandler) ListCustomers(c *gin.Context) {
	var query model.CustomerListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	// Get current tenant ID from context
	if tenantID, ok := auth.GetCurrentTenantID(c); ok {
		query.TenantID = tenantID
	}

	customers, total, err := h.customerService.ListCustomers(c.Request.Context(), query)
	if err != nil {
		h.handleError(c, err)
		return
	}

	// Convert to response DTOs
	responses := make([]model.CustomerResponse, len(customers))
	for i, customer := range customers {
		responses[i] = customer.ToCustomerResponse()
	}

	pagination := types.NewPagination(query.Page, query.PageSize, total)
	types.PaginatedSuccessResponse(c, responses, pagination)
}

// GetCustomerStats handles customer statistics requests
func (h *CustomerHandler) GetCustomerStats(c *gin.Context) {
	tenantID, ok := auth.GetCurrentTenantID(c)
	if !ok {
		types.ErrorResponse(c, http.StatusUnauthorized, types.ErrorCodeUnauthorized, "Tenant not found")
		return
	}

	stats, err := h.customerService.GetCustomerStats(c.Request.Context(), tenantID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponse(c, stats)
}

// AddCustomerToGroup handles adding customer to group requests
func (h *CustomerHandler) AddCustomerToGroup(c *gin.Context) {
	idStr := c.Param("id")
	customerID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid customer ID")
		return
	}

	var req struct {
		GroupID uint64 `json:"group_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	if err := h.customerService.AddCustomerToGroup(c.Request.Context(), customerID, req.GroupID); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Customer added to group successfully", nil)
}

// RemoveCustomerFromGroup handles removing customer from group requests
func (h *CustomerHandler) RemoveCustomerFromGroup(c *gin.Context) {
	idStr := c.Param("id")
	customerID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid customer ID")
		return
	}

	var req struct {
		GroupID uint64 `json:"group_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	if err := h.customerService.RemoveCustomerFromGroup(c.Request.Context(), customerID, req.GroupID); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Customer removed from group successfully", nil)
}

// handleError handles service errors and converts them to appropriate HTTP responses
func (h *CustomerHandler) handleError(c *gin.Context, err error) {
	h.logger.Error("Customer handler error", zap.Error(err))

	switch err {
	case model.ErrCustomerNotFound:
		types.ErrorResponse(c, http.StatusNotFound, model.GetErrorCode(err), err.Error())
	case model.ErrCustomerAlreadyExists:
		types.ErrorResponse(c, http.StatusConflict, model.GetErrorCode(err), err.Error())
	case model.ErrCustomerBlocked:
		types.ErrorResponse(c, http.StatusForbidden, model.GetErrorCode(err), err.Error())
	case model.ErrInvalidCustomerCode:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrInvalidEmail:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrInvalidPhone:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrInvalidGender:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrCustomerHasActivities:
		types.ErrorResponse(c, http.StatusConflict, model.GetErrorCode(err), err.Error())
	case model.ErrGroupNotFound:
		types.ErrorResponse(c, http.StatusNotFound, model.GetErrorCode(err), err.Error())
	case model.ErrGroupInactive:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrCustomerAlreadyInGroup:
		types.ErrorResponse(c, http.StatusConflict, model.GetErrorCode(err), err.Error())
	case model.ErrCustomerNotInGroup:
		types.ErrorResponse(c, http.StatusNotFound, model.GetErrorCode(err), err.Error())
	default:
		types.ErrorResponse(c, http.StatusInternalServerError, types.ErrorCodeInternalError, "Internal server error")
	}
}
