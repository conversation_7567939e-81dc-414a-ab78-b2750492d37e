package model

import "errors"

// User related errors
var (
	ErrUserNotFound         = errors.New("user not found")
	ErrUserAlreadyExists    = errors.New("user already exists")
	ErrUserInactive         = errors.New("user is inactive")
	ErrUserLocked           = errors.New("user is locked")
	ErrUserSuspended        = errors.New("user is suspended")
	ErrInvalidCredentials   = errors.New("invalid credentials")
	ErrPasswordTooWeak      = errors.New("password is too weak")
	ErrSamePassword         = errors.New("new password cannot be the same as current password")
	ErrTooManyFailedLogins  = errors.New("too many failed login attempts")
	ErrInvalidToken         = errors.New("invalid token")
	ErrTokenExpired         = errors.New("token expired")
	ErrRefreshTokenInvalid  = errors.New("refresh token is invalid")
)

// Role related errors
var (
	ErrRoleNotFound      = errors.New("role not found")
	ErrRoleAlreadyExists = errors.New("role already exists")
	ErrRoleInactive      = errors.New("role is inactive")
	ErrInvalidPermission = errors.New("invalid permission")
	ErrCannotDeleteRole  = errors.New("cannot delete role with assigned users")
)

// Permission related errors
var (
	ErrInsufficientPermissions = errors.New("insufficient permissions")
	ErrPermissionDenied        = errors.New("permission denied")
	ErrInvalidTenant           = errors.New("invalid tenant")
)

// Validation errors
var (
	ErrInvalidEmail    = errors.New("invalid email format")
	ErrInvalidPhone    = errors.New("invalid phone format")
	ErrInvalidUsername = errors.New("invalid username format")
	ErrRequiredField   = errors.New("required field is missing")
)

// Error codes for API responses
const (
	ErrorCodeUserNotFound         = "USER_NOT_FOUND"
	ErrorCodeUserAlreadyExists    = "USER_ALREADY_EXISTS"
	ErrorCodeUserInactive         = "USER_INACTIVE"
	ErrorCodeUserLocked           = "USER_LOCKED"
	ErrorCodeUserSuspended        = "USER_SUSPENDED"
	ErrorCodeInvalidCredentials   = "INVALID_CREDENTIALS"
	ErrorCodePasswordTooWeak      = "PASSWORD_TOO_WEAK"
	ErrorCodeSamePassword         = "SAME_PASSWORD"
	ErrorCodeTooManyFailedLogins  = "TOO_MANY_FAILED_LOGINS"
	ErrorCodeInvalidToken         = "INVALID_TOKEN"
	ErrorCodeTokenExpired         = "TOKEN_EXPIRED"
	ErrorCodeRefreshTokenInvalid  = "REFRESH_TOKEN_INVALID"
	ErrorCodeRoleNotFound         = "ROLE_NOT_FOUND"
	ErrorCodeRoleAlreadyExists    = "ROLE_ALREADY_EXISTS"
	ErrorCodeRoleInactive         = "ROLE_INACTIVE"
	ErrorCodeInvalidPermission    = "INVALID_PERMISSION"
	ErrorCodeCannotDeleteRole     = "CANNOT_DELETE_ROLE"
	ErrorCodeInsufficientPermissions = "INSUFFICIENT_PERMISSIONS"
	ErrorCodePermissionDenied     = "PERMISSION_DENIED"
	ErrorCodeInvalidTenant        = "INVALID_TENANT"
	ErrorCodeInvalidEmail         = "INVALID_EMAIL"
	ErrorCodeInvalidPhone         = "INVALID_PHONE"
	ErrorCodeInvalidUsername      = "INVALID_USERNAME"
	ErrorCodeRequiredField        = "REQUIRED_FIELD"
)

// GetErrorCode returns the error code for a given error
func GetErrorCode(err error) string {
	switch err {
	case ErrUserNotFound:
		return ErrorCodeUserNotFound
	case ErrUserAlreadyExists:
		return ErrorCodeUserAlreadyExists
	case ErrUserInactive:
		return ErrorCodeUserInactive
	case ErrUserLocked:
		return ErrorCodeUserLocked
	case ErrUserSuspended:
		return ErrorCodeUserSuspended
	case ErrInvalidCredentials:
		return ErrorCodeInvalidCredentials
	case ErrPasswordTooWeak:
		return ErrorCodePasswordTooWeak
	case ErrSamePassword:
		return ErrorCodeSamePassword
	case ErrTooManyFailedLogins:
		return ErrorCodeTooManyFailedLogins
	case ErrInvalidToken:
		return ErrorCodeInvalidToken
	case ErrTokenExpired:
		return ErrorCodeTokenExpired
	case ErrRefreshTokenInvalid:
		return ErrorCodeRefreshTokenInvalid
	case ErrRoleNotFound:
		return ErrorCodeRoleNotFound
	case ErrRoleAlreadyExists:
		return ErrorCodeRoleAlreadyExists
	case ErrRoleInactive:
		return ErrorCodeRoleInactive
	case ErrInvalidPermission:
		return ErrorCodeInvalidPermission
	case ErrCannotDeleteRole:
		return ErrorCodeCannotDeleteRole
	case ErrInsufficientPermissions:
		return ErrorCodeInsufficientPermissions
	case ErrPermissionDenied:
		return ErrorCodePermissionDenied
	case ErrInvalidTenant:
		return ErrorCodeInvalidTenant
	case ErrInvalidEmail:
		return ErrorCodeInvalidEmail
	case ErrInvalidPhone:
		return ErrorCodeInvalidPhone
	case ErrInvalidUsername:
		return ErrorCodeInvalidUsername
	case ErrRequiredField:
		return ErrorCodeRequiredField
	default:
		return "UNKNOWN_ERROR"
	}
}
