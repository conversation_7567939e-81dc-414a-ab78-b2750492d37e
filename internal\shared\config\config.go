package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Cache    CacheConfig    `mapstructure:"cache"`
	Queue    QueueConfig    `mapstructure:"queue"`
	Auth     AuthConfig     `mapstructure:"auth"`
	External ExternalConfig `mapstructure:"external"`
}

type ServerConfig struct {
	Port         int    `mapstructure:"port"`
	Mode         string `mapstructure:"mode"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
}

type DatabaseConfig struct {
	MySQL      MySQLConfig      `mapstructure:"mysql"`
	Redis      RedisConfig      `mapstructure:"redis"`
	ClickHouse ClickHouseConfig `mapstructure:"clickhouse"`
}

type MySQLConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
	Database     string `mapstructure:"database"`
	MaxOpenConns int    `mapstructure:"max_open_conns"`
	MaxIdleConns int    `mapstructure:"max_idle_conns"`
	MaxLifetime  int    `mapstructure:"max_lifetime"`
}

type RedisConfig struct {
	Addresses []string `mapstructure:"addresses"`
	Password  string   `mapstructure:"password"`
	DB        int      `mapstructure:"db"`
	PoolSize  int      `mapstructure:"pool_size"`
}

type ClickHouseConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
}

type CacheConfig struct {
	DefaultTTL int `mapstructure:"default_ttl"`
}

type QueueConfig struct {
	Kafka KafkaConfig `mapstructure:"kafka"`
}

type KafkaConfig struct {
	Brokers []string `mapstructure:"brokers"`
	GroupID string   `mapstructure:"group_id"`
}

type AuthConfig struct {
	JWTSecret     string `mapstructure:"jwt_secret"`
	TokenExpiry   int    `mapstructure:"token_expiry"`
	RefreshExpiry int    `mapstructure:"refresh_expiry"`
}

type ExternalConfig struct {
	SMS      SMSConfig      `mapstructure:"sms"`
	WhatsApp WhatsAppConfig `mapstructure:"whatsapp"`
	WeChat   WeChatConfig   `mapstructure:"wechat"`
}

type SMSConfig struct {
	Gateway string `mapstructure:"gateway"`
	APIKey  string `mapstructure:"api_key"`
	Secret  string `mapstructure:"secret"`
}

type WhatsAppConfig struct {
	BusinessAPIURL string `mapstructure:"business_api_url"`
	AccessToken    string `mapstructure:"access_token"`
	PhoneNumberID  string `mapstructure:"phone_number_id"`
}

type WeChatConfig struct {
	AppID     string `mapstructure:"app_id"`
	AppSecret string `mapstructure:"app_secret"`
}
