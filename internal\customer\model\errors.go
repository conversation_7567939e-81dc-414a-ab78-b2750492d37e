package model

import "errors"

// Customer related errors
var (
	ErrCustomerNotFound      = errors.New("customer not found")
	ErrCustomerAlreadyExists = errors.New("customer already exists")
	ErrCustomerBlocked       = errors.New("customer is blocked")
	ErrInvalidCustomerCode   = errors.New("invalid customer code")
	ErrInvalidEmail          = errors.New("invalid email format")
	ErrInvalidPhone          = errors.New("invalid phone format")
	ErrInvalidGender         = errors.New("invalid gender")
	ErrInvalidDateOfBirth    = errors.New("invalid date of birth")
	ErrCustomerHasActivities = errors.New("customer has activities and cannot be deleted")
)

// Customer group related errors
var (
	ErrGroupNotFound         = errors.New("customer group not found")
	ErrGroupAlreadyExists    = errors.New("customer group already exists")
	ErrGroupInactive         = errors.New("customer group is inactive")
	ErrInvalidGroupCode      = errors.New("invalid group code")
	ErrInvalidGroupCondition = errors.New("invalid group condition")
	ErrGroupHasMembers       = errors.New("group has members and cannot be deleted")
	ErrCustomerAlreadyInGroup = errors.New("customer already in group")
	ErrCustomerNotInGroup    = errors.New("customer not in group")
)

// Customer tag related errors
var (
	ErrTagNotFound      = errors.New("customer tag not found")
	ErrTagAlreadyExists = errors.New("customer tag already exists")
	ErrTagInactive      = errors.New("customer tag is inactive")
	ErrInvalidTagName   = errors.New("invalid tag name")
	ErrInvalidTagColor  = errors.New("invalid tag color")
	ErrTagInUse         = errors.New("tag is in use and cannot be deleted")
)

// Activity related errors
var (
	ErrActivityNotFound     = errors.New("customer activity not found")
	ErrInvalidActivityType  = errors.New("invalid activity type")
	ErrInvalidActivityData  = errors.New("invalid activity data")
)

// Error codes for API responses
const (
	ErrorCodeCustomerNotFound      = "CUSTOMER_NOT_FOUND"
	ErrorCodeCustomerAlreadyExists = "CUSTOMER_ALREADY_EXISTS"
	ErrorCodeCustomerBlocked       = "CUSTOMER_BLOCKED"
	ErrorCodeInvalidCustomerCode   = "INVALID_CUSTOMER_CODE"
	ErrorCodeInvalidEmail          = "INVALID_EMAIL"
	ErrorCodeInvalidPhone          = "INVALID_PHONE"
	ErrorCodeInvalidGender         = "INVALID_GENDER"
	ErrorCodeInvalidDateOfBirth    = "INVALID_DATE_OF_BIRTH"
	ErrorCodeCustomerHasActivities = "CUSTOMER_HAS_ACTIVITIES"
	ErrorCodeGroupNotFound         = "GROUP_NOT_FOUND"
	ErrorCodeGroupAlreadyExists    = "GROUP_ALREADY_EXISTS"
	ErrorCodeGroupInactive         = "GROUP_INACTIVE"
	ErrorCodeInvalidGroupCode      = "INVALID_GROUP_CODE"
	ErrorCodeInvalidGroupCondition = "INVALID_GROUP_CONDITION"
	ErrorCodeGroupHasMembers       = "GROUP_HAS_MEMBERS"
	ErrorCodeCustomerAlreadyInGroup = "CUSTOMER_ALREADY_IN_GROUP"
	ErrorCodeCustomerNotInGroup    = "CUSTOMER_NOT_IN_GROUP"
	ErrorCodeTagNotFound           = "TAG_NOT_FOUND"
	ErrorCodeTagAlreadyExists      = "TAG_ALREADY_EXISTS"
	ErrorCodeTagInactive           = "TAG_INACTIVE"
	ErrorCodeInvalidTagName        = "INVALID_TAG_NAME"
	ErrorCodeInvalidTagColor       = "INVALID_TAG_COLOR"
	ErrorCodeTagInUse              = "TAG_IN_USE"
	ErrorCodeActivityNotFound      = "ACTIVITY_NOT_FOUND"
	ErrorCodeInvalidActivityType   = "INVALID_ACTIVITY_TYPE"
	ErrorCodeInvalidActivityData   = "INVALID_ACTIVITY_DATA"
)

// GetErrorCode returns the error code for a given error
func GetErrorCode(err error) string {
	switch err {
	case ErrCustomerNotFound:
		return ErrorCodeCustomerNotFound
	case ErrCustomerAlreadyExists:
		return ErrorCodeCustomerAlreadyExists
	case ErrCustomerBlocked:
		return ErrorCodeCustomerBlocked
	case ErrInvalidCustomerCode:
		return ErrorCodeInvalidCustomerCode
	case ErrInvalidEmail:
		return ErrorCodeInvalidEmail
	case ErrInvalidPhone:
		return ErrorCodeInvalidPhone
	case ErrInvalidGender:
		return ErrorCodeInvalidGender
	case ErrInvalidDateOfBirth:
		return ErrorCodeInvalidDateOfBirth
	case ErrCustomerHasActivities:
		return ErrorCodeCustomerHasActivities
	case ErrGroupNotFound:
		return ErrorCodeGroupNotFound
	case ErrGroupAlreadyExists:
		return ErrorCodeGroupAlreadyExists
	case ErrGroupInactive:
		return ErrorCodeGroupInactive
	case ErrInvalidGroupCode:
		return ErrorCodeInvalidGroupCode
	case ErrInvalidGroupCondition:
		return ErrorCodeInvalidGroupCondition
	case ErrGroupHasMembers:
		return ErrorCodeGroupHasMembers
	case ErrCustomerAlreadyInGroup:
		return ErrorCodeCustomerAlreadyInGroup
	case ErrCustomerNotInGroup:
		return ErrorCodeCustomerNotInGroup
	case ErrTagNotFound:
		return ErrorCodeTagNotFound
	case ErrTagAlreadyExists:
		return ErrorCodeTagAlreadyExists
	case ErrTagInactive:
		return ErrorCodeTagInactive
	case ErrInvalidTagName:
		return ErrorCodeInvalidTagName
	case ErrInvalidTagColor:
		return ErrorCodeInvalidTagColor
	case ErrTagInUse:
		return ErrorCodeTagInUse
	case ErrActivityNotFound:
		return ErrorCodeActivityNotFound
	case ErrInvalidActivityType:
		return ErrorCodeInvalidActivityType
	case ErrInvalidActivityData:
		return ErrorCodeInvalidActivityData
	default:
		return "UNKNOWN_ERROR"
	}
}
