# CMHK Communication Platform - Development Configuration
server:
  port: 8080
  mode: development
  read_timeout: 30
  write_timeout: 30

database:
  mysql:
    host: localhost
    port: 3306
    username: cmhk_dev
    password: dev_password
    database: cmhk_platform_dev
    max_open_conns: 50
    max_idle_conns: 5
    max_lifetime: 1800
  
  redis:
    addresses:
      - localhost:6379
    password: ""
    db: 1
    pool_size: 50
  
  clickhouse:
    host: localhost
    port: 9000
    username: default
    password: ""
    database: cmhk_analytics_dev

cache:
  default_ttl: 1800

auth:
  jwt_secret: dev-secret-key-change-in-production
  token_expiry: 3600
  refresh_expiry: 86400

external:
  sms:
    gateway: "https://api-dev.sms.com"
    api_key: "dev-api-key"
    secret: "dev-secret"
  
  whatsapp:
    business_api_url: "https://graph.facebook.com"
    access_token: "dev-access-token"
    phone_number_id: "dev-phone-number-id"
  
  wechat:
    app_id: "dev-app-id"
    app_secret: "dev-app-secret"
