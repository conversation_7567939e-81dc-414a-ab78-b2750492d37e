# CMHK Communication Platform Configuration
server:
  port: 8080
  mode: development
  read_timeout: 30
  write_timeout: 30

database:
  mysql:
    host: localhost
    port: 3306
    username: cmhk
    password: password
    database: cmhk_platform
    max_open_conns: 100
    max_idle_conns: 10
    max_lifetime: 3600
  
  redis:
    addresses:
      - localhost:6379
    password: ""
    db: 0
    pool_size: 100
  
  clickhouse:
    host: localhost
    port: 9000
    username: default
    password: ""
    database: cmhk_analytics

cache:
  default_ttl: 3600

auth:
  jwt_secret: your-secret-key
  token_expiry: 3600
  refresh_expiry: 86400

external:
  sms:
    gateway: "https://api.sms.com"
    api_key: "your-api-key"
    secret: "your-secret"
  
  whatsapp:
    business_api_url: "https://graph.facebook.com"
    access_token: "your-access-token"
    phone_number_id: "your-phone-number-id"
  
  wechat:
    app_id: "your-app-id"
    app_secret: "your-app-secret"
