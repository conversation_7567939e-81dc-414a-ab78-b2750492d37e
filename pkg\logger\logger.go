package logger

import (
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger wraps zap.Logger with additional functionality
type Logger struct {
	*zap.Logger
}

// NewLogger creates a new logger instance
func NewLogger() *Logger {
	config := zap.NewProductionConfig()

	// Set log level based on environment
	if os.Getenv("CMHK_SERVER_MODE") == "development" {
		config = zap.NewDevelopmentConfig()
		config.Level = zap.NewAtomicLevelAt(zap.DebugLevel)
	} else {
		config.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
	}

	// Configure encoder
	config.EncoderConfig.TimeKey = "timestamp"
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	config.EncoderConfig.StacktraceKey = ""

	logger, err := config.Build()
	if err != nil {
		panic(err)
	}

	return &Logger{Logger: logger}
}

// NewLoggerWithConfig creates a logger with custom configuration
func NewLoggerWithConfig(level zapcore.Level, isDevelopment bool) *Logger {
	var config zap.Config

	if isDevelopment {
		config = zap.NewDevelopmentConfig()
	} else {
		config = zap.NewProductionConfig()
	}

	config.Level = zap.NewAtomicLevelAt(level)
	config.EncoderConfig.TimeKey = "timestamp"
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	config.EncoderConfig.StacktraceKey = ""

	logger, err := config.Build()
	if err != nil {
		panic(err)
	}

	return &Logger{Logger: logger}
}

// WithRequestID adds request ID to logger context
func (l *Logger) WithRequestID(requestID string) *Logger {
	return &Logger{Logger: l.Logger.With(zap.String("request_id", requestID))}
}

// WithUserID adds user ID to logger context
func (l *Logger) WithUserID(userID string) *Logger {
	return &Logger{Logger: l.Logger.With(zap.String("user_id", userID))}
}

// WithModule adds module name to logger context
func (l *Logger) WithModule(module string) *Logger {
	return &Logger{Logger: l.Logger.With(zap.String("module", module))}
}

// WithError adds error to logger context
func (l *Logger) WithError(err error) *Logger {
	return &Logger{Logger: l.Logger.With(zap.Error(err))}
}

// LogHTTPRequest logs HTTP request information
func (l *Logger) LogHTTPRequest(method, path, userAgent, clientIP string, statusCode int, duration int64) {
	l.Info("HTTP Request",
		zap.String("method", method),
		zap.String("path", path),
		zap.String("user_agent", userAgent),
		zap.String("client_ip", clientIP),
		zap.Int("status_code", statusCode),
		zap.Int64("duration_ms", duration),
	)
}

// LogDatabaseQuery logs database query information
func (l *Logger) LogDatabaseQuery(query string, duration int64, rowsAffected int64) {
	l.Debug("Database Query",
		zap.String("query", query),
		zap.Int64("duration_ms", duration),
		zap.Int64("rows_affected", rowsAffected),
	)
}
