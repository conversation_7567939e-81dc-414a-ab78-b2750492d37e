package cache

import (
	"context"
	"encoding/json"
	"time"
)

// Cache defines the interface for cache operations
type Cache interface {
	Get(ctx context.Context, key string) (string, error)
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	Expire(ctx context.Context, key string, expiration time.Duration) error
	GetJSON(ctx context.Context, key string, dest interface{}) error
	SetJSON(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	GetMultiple(ctx context.Context, keys []string) (map[string]string, error)
	SetMultiple(ctx context.Context, data map[string]interface{}, expiration time.Duration) error
	DeleteMultiple(ctx context.Context, keys []string) error
	FlushAll(ctx context.Context) error
}

// CacheManager provides cache management functionality
type CacheManager struct {
	cache      Cache
	defaultTTL time.Duration
}

// NewCacheManager creates a new cache manager
func NewCacheManager(cache Cache, defaultTTL time.Duration) *CacheManager {
	return &CacheManager{
		cache:      cache,
		defaultTTL: defaultTTL,
	}
}

// Get retrieves a value from cache
func (cm *CacheManager) Get(ctx context.Context, key string) (string, error) {
	return cm.cache.Get(ctx, key)
}

// Set stores a value in cache with default TTL
func (cm *CacheManager) Set(ctx context.Context, key string, value interface{}) error {
	return cm.cache.Set(ctx, key, value, cm.defaultTTL)
}

// SetWithTTL stores a value in cache with custom TTL
func (cm *CacheManager) SetWithTTL(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	return cm.cache.Set(ctx, key, value, ttl)
}

// Delete removes a value from cache
func (cm *CacheManager) Delete(ctx context.Context, key string) error {
	return cm.cache.Delete(ctx, key)
}

// GetJSON retrieves and unmarshals JSON data from cache
func (cm *CacheManager) GetJSON(ctx context.Context, key string, dest interface{}) error {
	return cm.cache.GetJSON(ctx, key, dest)
}

// SetJSON marshals and stores JSON data in cache
func (cm *CacheManager) SetJSON(ctx context.Context, key string, value interface{}) error {
	return cm.cache.SetJSON(ctx, key, value, cm.defaultTTL)
}

// SetJSONWithTTL marshals and stores JSON data in cache with custom TTL
func (cm *CacheManager) SetJSONWithTTL(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	return cm.cache.SetJSON(ctx, key, value, ttl)
}

// Exists checks if a key exists in cache
func (cm *CacheManager) Exists(ctx context.Context, key string) (bool, error) {
	return cm.cache.Exists(ctx, key)
}

// CacheKey generates a cache key with prefix
func CacheKey(prefix, key string) string {
	return prefix + ":" + key
}

// UserCacheKey generates a user-specific cache key
func UserCacheKey(userID, key string) string {
	return CacheKey("user:"+userID, key)
}

// SessionCacheKey generates a session-specific cache key
func SessionCacheKey(sessionID string) string {
	return CacheKey("session", sessionID)
}

// TokenCacheKey generates a token-specific cache key
func TokenCacheKey(tokenType, token string) string {
	return CacheKey("token:"+tokenType, token)
}

// MarshalJSON marshals data to JSON string
func MarshalJSON(data interface{}) (string, error) {
	bytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// UnmarshalJSON unmarshals JSON string to data
func UnmarshalJSON(jsonStr string, dest interface{}) error {
	return json.Unmarshal([]byte(jsonStr), dest)
}
