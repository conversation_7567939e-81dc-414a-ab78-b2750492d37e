apiVersion: apps/v1
kind: Deployment
metadata:
  name: cmhk-platform
  namespace: cmhk
  labels:
    app: cmhk-platform
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: cmhk-platform
  template:
    metadata:
      labels:
        app: cmhk-platform
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: cmhk-platform
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: cmhk-platform
        image: cmhk-platform:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        env:
        - name: APP_ENV
          value: "production"
        - name: LOG_LEVEL
          value: "info"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: cmhk-platform-secrets
              key: db-host
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: cmhk-platform-secrets
              key: db-user
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cmhk-platform-secrets
              key: db-password
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: cmhk-platform-secrets
              key: db-name
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: cmhk-platform-secrets
              key: redis-host
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: cmhk-platform-secrets
              key: redis-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: cmhk-platform-secrets
              key: jwt-secret
        volumeMounts:
        - name: config
          mountPath: /app/configs
          readOnly: true
        - name: logs
          mountPath: /app/logs
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config
        configMap:
          name: cmhk-platform-config
      - name: logs
        emptyDir: {}
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - cmhk-platform
              topologyKey: kubernetes.io/hostname

---
apiVersion: v1
kind: Service
metadata:
  name: cmhk-platform-service
  namespace: cmhk
  labels:
    app: cmhk-platform
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: cmhk-platform

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cmhk-platform-config
  namespace: cmhk
data:
  app.yaml: |
    server:
      port: 8080
      read_timeout: 30s
      write_timeout: 30s
      idle_timeout: 120s
    
    database:
      mysql:
        max_open_conns: 100
        max_idle_conns: 10
        conn_max_lifetime: 1h
        conn_max_idle_time: 10m
      
      redis:
        pool_size: 10
        min_idle_conns: 5
        dial_timeout: 5s
        read_timeout: 3s
        write_timeout: 3s
    
    cache:
      default_ttl: 15m
      enable_metrics: true
    
    security:
      rate_limit:
        global_rps: 1000
        per_ip_rps: 100
        per_user_rps: 200
      
      validation:
        enable_sql_injection_check: true
        enable_xss_check: true
        max_request_size: ********  # 10MB

---
apiVersion: v1
kind: Secret
metadata:
  name: cmhk-platform-secrets
  namespace: cmhk
type: Opaque
data:
  db-host: bXlzcWw=  # mysql (base64)
  db-user: cm9vdA==  # root (base64)
  db-password: cGFzc3dvcmQ=  # password (base64)
  db-name: Y21oa19wbGF0Zm9ybQ==  # cmhk_platform (base64)
  redis-host: cmVkaXM=  # redis (base64)
  redis-password: ""
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWtleQ==  # your-jwt-secret-key (base64)

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cmhk-platform
  namespace: cmhk

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cmhk-platform-ingress
  namespace: cmhk
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.cmhk-platform.com
    secretName: cmhk-platform-tls
  rules:
  - host: api.cmhk-platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cmhk-platform-service
            port:
              number: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cmhk-platform-hpa
  namespace: cmhk
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cmhk-platform
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
