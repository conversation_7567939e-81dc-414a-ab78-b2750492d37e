package model

import (
	"time"
	"gorm.io/gorm"
)

// Campaign represents a marketing campaign
type Campaign struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_status" json:"tenant_id"`
	CampaignCode string        `gorm:"size:50;not null;uniqueIndex:uk_tenant_campaign_code" json:"campaign_code"`
	CampaignName string        `gorm:"size:100;not null" json:"campaign_name"`
	Description string         `gorm:"type:text" json:"description"`
	CampaignType string        `gorm:"size:20;not null" json:"campaign_type"`
	Channel     string         `gorm:"size:20;not null" json:"channel"`
	Status      int8           `gorm:"default:0;index:idx_tenant_status" json:"status"`
	StartTime   *time.Time     `json:"start_time"`
	EndTime     *time.Time     `json:"end_time"`
	TargetAudience string       `gorm:"type:json" json:"target_audience"`
	MessageTemplate string      `gorm:"type:text" json:"message_template"`
	Settings    string         `gorm:"type:json" json:"settings"`
	Budget      float64        `gorm:"type:decimal(10,2)" json:"budget"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Executions []CampaignExecution `gorm:"foreignKey:CampaignID" json:"executions,omitempty"`
	Analytics  []CampaignAnalytics `gorm:"foreignKey:CampaignID" json:"analytics,omitempty"`
}

// CampaignExecution represents a campaign execution record
type CampaignExecution struct {
	ID           uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID     uint64         `gorm:"not null;index:idx_tenant_campaign" json:"tenant_id"`
	CampaignID   uint64         `gorm:"not null;index:idx_tenant_campaign" json:"campaign_id"`
	ExecutionType string        `gorm:"size:20;not null" json:"execution_type"`
	Status       string         `gorm:"size:20;default:pending" json:"status"`
	TargetCount  int64          `gorm:"default:0" json:"target_count"`
	SentCount    int64          `gorm:"default:0" json:"sent_count"`
	DeliveredCount int64        `gorm:"default:0" json:"delivered_count"`
	FailedCount  int64          `gorm:"default:0" json:"failed_count"`
	StartedAt    *time.Time     `json:"started_at"`
	CompletedAt  *time.Time     `json:"completed_at"`
	ErrorMessage string         `gorm:"type:text" json:"error_message"`
	Metadata     string         `gorm:"type:json" json:"metadata"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// CampaignAnalytics represents campaign analytics data
type CampaignAnalytics struct {
	ID           uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID     uint64    `gorm:"not null;index:idx_tenant_campaign" json:"tenant_id"`
	CampaignID   uint64    `gorm:"not null;index:idx_tenant_campaign" json:"campaign_id"`
	MetricName   string    `gorm:"size:50;not null" json:"metric_name"`
	MetricValue  float64   `gorm:"type:decimal(15,4)" json:"metric_value"`
	MetricDate   time.Time `gorm:"index:idx_metric_date" json:"metric_date"`
	Dimensions   string    `gorm:"type:json" json:"dimensions"`
	CreatedAt    time.Time `json:"created_at"`
}

// CampaignTarget represents campaign target audience
type CampaignTarget struct {
	ID         uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID   uint64    `gorm:"not null;index:idx_tenant_campaign" json:"tenant_id"`
	CampaignID uint64    `gorm:"not null;index:idx_tenant_campaign" json:"campaign_id"`
	TargetType string    `gorm:"size:20;not null" json:"target_type"`
	TargetID   uint64    `gorm:"not null" json:"target_id"`
	Status     string    `gorm:"size:20;default:active" json:"status"`
	CreatedAt  time.Time `json:"created_at"`
}

// Campaign types
const (
	CampaignTypePromotion    = "promotion"
	CampaignTypeNotification = "notification"
	CampaignTypeWelcome      = "welcome"
	CampaignTypeRetention    = "retention"
	CampaignTypeWinback      = "winback"
	CampaignTypeSurvey       = "survey"
)

// Campaign statuses
const (
	CampaignStatusDraft     = 0
	CampaignStatusScheduled = 1
	CampaignStatusRunning   = 2
	CampaignStatusPaused    = 3
	CampaignStatusCompleted = 4
	CampaignStatusCancelled = 5
)

// Execution types
const (
	ExecutionTypeImmediate = "immediate"
	ExecutionTypeScheduled = "scheduled"
	ExecutionTypeTrigger   = "trigger"
	ExecutionTypeRecurring = "recurring"
)

// Execution statuses
const (
	ExecutionStatusPending   = "pending"
	ExecutionStatusRunning   = "running"
	ExecutionStatusCompleted = "completed"
	ExecutionStatusFailed    = "failed"
	ExecutionStatusCancelled = "cancelled"
)

// Target types
const (
	TargetTypeCustomer      = "customer"
	TargetTypeCustomerGroup = "customer_group"
	TargetTypeSegment       = "segment"
)

// Metric names
const (
	MetricSent        = "sent"
	MetricDelivered   = "delivered"
	MetricOpened      = "opened"
	MetricClicked     = "clicked"
	MetricConverted   = "converted"
	MetricUnsubscribed = "unsubscribed"
	MetricBounced     = "bounced"
)

// TableName returns the table name for Campaign
func (Campaign) TableName() string {
	return "campaigns"
}

// TableName returns the table name for CampaignExecution
func (CampaignExecution) TableName() string {
	return "campaign_executions"
}

// TableName returns the table name for CampaignAnalytics
func (CampaignAnalytics) TableName() string {
	return "campaign_analytics"
}

// TableName returns the table name for CampaignTarget
func (CampaignTarget) TableName() string {
	return "campaign_targets"
}

// IsActive checks if campaign is active
func (c *Campaign) IsActive() bool {
	return c.Status == CampaignStatusRunning
}

// IsScheduled checks if campaign is scheduled
func (c *Campaign) IsScheduled() bool {
	return c.Status == CampaignStatusScheduled
}

// CanStart checks if campaign can be started
func (c *Campaign) CanStart() bool {
	return c.Status == CampaignStatusDraft || c.Status == CampaignStatusScheduled
}

// CanPause checks if campaign can be paused
func (c *Campaign) CanPause() bool {
	return c.Status == CampaignStatusRunning
}

// CanResume checks if campaign can be resumed
func (c *Campaign) CanResume() bool {
	return c.Status == CampaignStatusPaused
}

// CanCancel checks if campaign can be cancelled
func (c *Campaign) CanCancel() bool {
	return c.Status == CampaignStatusDraft || c.Status == CampaignStatusScheduled || 
		   c.Status == CampaignStatusRunning || c.Status == CampaignStatusPaused
}

// IsCompleted checks if execution is completed
func (e *CampaignExecution) IsCompleted() bool {
	return e.Status == ExecutionStatusCompleted
}

// IsFailed checks if execution failed
func (e *CampaignExecution) IsFailed() bool {
	return e.Status == ExecutionStatusFailed
}

// CanRetry checks if execution can be retried
func (e *CampaignExecution) CanRetry() bool {
	return e.Status == ExecutionStatusFailed
}

// GetSuccessRate calculates the success rate of execution
func (e *CampaignExecution) GetSuccessRate() float64 {
	if e.SentCount == 0 {
		return 0
	}
	return float64(e.DeliveredCount) / float64(e.SentCount) * 100
}

// ValidateCampaignType validates if campaign type is supported
func ValidateCampaignType(campaignType string) bool {
	supportedTypes := []string{
		CampaignTypePromotion,
		CampaignTypeNotification,
		CampaignTypeWelcome,
		CampaignTypeRetention,
		CampaignTypeWinback,
		CampaignTypeSurvey,
	}
	
	for _, supported := range supportedTypes {
		if campaignType == supported {
			return true
		}
	}
	
	return false
}

// ValidateExecutionType validates if execution type is supported
func ValidateExecutionType(executionType string) bool {
	supportedTypes := []string{
		ExecutionTypeImmediate,
		ExecutionTypeScheduled,
		ExecutionTypeTrigger,
		ExecutionTypeRecurring,
	}
	
	for _, supported := range supportedTypes {
		if executionType == supported {
			return true
		}
	}
	
	return false
}
