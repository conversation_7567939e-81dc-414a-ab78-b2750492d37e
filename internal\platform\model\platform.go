package model

import (
	"time"
	"gorm.io/gorm"
)

// SystemConfig represents system configuration
type SystemConfig struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_key" json:"tenant_id"`
	ConfigKey   string         `gorm:"size:100;not null;index:idx_tenant_key" json:"config_key"`
	ConfigValue string         `gorm:"type:text;not null" json:"config_value"`
	ConfigType  string         `gorm:"size:20;not null" json:"config_type"`
	Category    string         `gorm:"size:50;not null" json:"category"`
	Description string         `gorm:"type:text" json:"description"`
	IsEncrypted bool           `gorm:"default:false" json:"is_encrypted"`
	IsPublic    bool           `gorm:"default:false" json:"is_public"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	UpdatedBy   uint64         `gorm:"not null" json:"updated_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// AuditLog represents audit log entries
type AuditLog struct {
	ID         uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID   uint64    `gorm:"not null;index:idx_tenant_user" json:"tenant_id"`
	UserID     uint64    `gorm:"not null;index:idx_tenant_user" json:"user_id"`
	Action     string    `gorm:"size:50;not null" json:"action"`
	Resource   string    `gorm:"size:100;not null" json:"resource"`
	ResourceID string    `gorm:"size:100" json:"resource_id"`
	Method     string    `gorm:"size:10" json:"method"`
	Path       string    `gorm:"size:255" json:"path"`
	IPAddress  string    `gorm:"size:45" json:"ip_address"`
	UserAgent  string    `gorm:"size:500" json:"user_agent"`
	RequestData string   `gorm:"type:text" json:"request_data"`
	ResponseData string  `gorm:"type:text" json:"response_data"`
	Status     int       `gorm:"not null" json:"status"`
	Duration   int64     `gorm:"default:0" json:"duration"` // milliseconds
	CreatedAt  time.Time `gorm:"index:idx_created_at" json:"created_at"`
}

// Permission represents a permission in the system
type Permission struct {
	ID           uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	PermissionCode string       `gorm:"size:100;not null;uniqueIndex" json:"permission_code"`
	PermissionName string       `gorm:"size:100;not null" json:"permission_name"`
	Description  string         `gorm:"type:text" json:"description"`
	Resource     string         `gorm:"size:100;not null" json:"resource"`
	Action       string         `gorm:"size:50;not null" json:"action"`
	Category     string         `gorm:"size:50;not null" json:"category"`
	IsSystem     bool           `gorm:"default:false" json:"is_system"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// RolePermission represents the many-to-many relationship between roles and permissions
type RolePermission struct {
	ID           uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	RoleID       uint64    `gorm:"not null;uniqueIndex:uk_role_permission" json:"role_id"`
	PermissionID uint64    `gorm:"not null;uniqueIndex:uk_role_permission" json:"permission_id"`
	GrantedBy    uint64    `gorm:"not null" json:"granted_by"`
	GrantedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"granted_at"`
}

// Tenant represents a tenant in the system
type Tenant struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantCode  string         `gorm:"size:50;not null;uniqueIndex" json:"tenant_code"`
	TenantName  string         `gorm:"size:100;not null" json:"tenant_name"`
	Description string         `gorm:"type:text" json:"description"`
	Domain      string         `gorm:"size:100" json:"domain"`
	Status      int8           `gorm:"default:1" json:"status"`
	Settings    string         `gorm:"type:json" json:"settings"`
	Quota       string         `gorm:"type:json" json:"quota"`
	ExpiresAt   *time.Time     `json:"expires_at"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// SystemNotification represents system notifications
type SystemNotification struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_type" json:"tenant_id"`
	Type        string         `gorm:"size:20;not null;index:idx_tenant_type" json:"type"`
	Title       string         `gorm:"size:200;not null" json:"title"`
	Content     string         `gorm:"type:text;not null" json:"content"`
	Priority    string         `gorm:"size:20;default:normal" json:"priority"`
	TargetUsers string         `gorm:"type:json" json:"target_users"`
	IsRead      bool           `gorm:"default:false" json:"is_read"`
	ReadBy      string         `gorm:"type:json" json:"read_by"`
	ExpiresAt   *time.Time     `json:"expires_at"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `gorm:"index:idx_created_at" json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// Config types
const (
	ConfigTypeString  = "string"
	ConfigTypeNumber  = "number"
	ConfigTypeBoolean = "boolean"
	ConfigTypeJSON    = "json"
	ConfigTypeArray   = "array"
)

// Config categories
const (
	CategorySystem       = "system"
	CategorySecurity     = "security"
	CategoryNotification = "notification"
	CategoryIntegration  = "integration"
	CategoryUI           = "ui"
	CategoryBusiness     = "business"
)

// Audit actions
const (
	ActionCreate = "create"
	ActionRead   = "read"
	ActionUpdate = "update"
	ActionDelete = "delete"
	ActionLogin  = "login"
	ActionLogout = "logout"
	ActionExport = "export"
	ActionImport = "import"
)

// Permission categories
const (
	PermissionCategoryUser         = "user"
	PermissionCategoryCommunication = "communication"
	PermissionCategoryCustomer     = "customer"
	PermissionCategoryMarketing    = "marketing"
	PermissionCategoryAnalytics    = "analytics"
	PermissionCategoryPlatform     = "platform"
)

// Tenant statuses
const (
	TenantStatusInactive = 0
	TenantStatusActive   = 1
	TenantStatusSuspended = 2
)

// Notification types
const (
	NotificationTypeSystem    = "system"
	NotificationTypeSecurity  = "security"
	NotificationTypeMarketing = "marketing"
	NotificationTypeMaintenance = "maintenance"
)

// Notification priorities
const (
	PriorityLow    = "low"
	PriorityNormal = "normal"
	PriorityHigh   = "high"
	PriorityUrgent = "urgent"
)

// TableName returns the table name for SystemConfig
func (SystemConfig) TableName() string {
	return "system_configs"
}

// TableName returns the table name for AuditLog
func (AuditLog) TableName() string {
	return "audit_logs"
}

// TableName returns the table name for Permission
func (Permission) TableName() string {
	return "permissions"
}

// TableName returns the table name for RolePermission
func (RolePermission) TableName() string {
	return "role_permissions"
}

// TableName returns the table name for Tenant
func (Tenant) TableName() string {
	return "tenants"
}

// TableName returns the table name for SystemNotification
func (SystemNotification) TableName() string {
	return "system_notifications"
}

// IsActive checks if tenant is active
func (t *Tenant) IsActive() bool {
	return t.Status == TenantStatusActive
}

// IsExpired checks if tenant is expired
func (t *Tenant) IsExpired() bool {
	return t.ExpiresAt != nil && t.ExpiresAt.Before(time.Now())
}

// IsRead checks if notification is read
func (n *SystemNotification) IsRead() bool {
	return n.IsRead
}

// IsExpired checks if notification is expired
func (n *SystemNotification) IsExpired() bool {
	return n.ExpiresAt != nil && n.ExpiresAt.Before(time.Now())
}

// ValidateConfigType validates if config type is supported
func ValidateConfigType(configType string) bool {
	supportedTypes := []string{
		ConfigTypeString,
		ConfigTypeNumber,
		ConfigTypeBoolean,
		ConfigTypeJSON,
		ConfigTypeArray,
	}
	
	for _, supported := range supportedTypes {
		if configType == supported {
			return true
		}
	}
	
	return false
}

// ValidateCategory validates if category is supported
func ValidateCategory(category string) bool {
	supportedCategories := []string{
		CategorySystem,
		CategorySecurity,
		CategoryNotification,
		CategoryIntegration,
		CategoryUI,
		CategoryBusiness,
	}
	
	for _, supported := range supportedCategories {
		if category == supported {
			return true
		}
	}
	
	return false
}

// ValidateNotificationType validates if notification type is supported
func ValidateNotificationType(notificationType string) bool {
	supportedTypes := []string{
		NotificationTypeSystem,
		NotificationTypeSecurity,
		NotificationTypeMarketing,
		NotificationTypeMaintenance,
	}
	
	for _, supported := range supportedTypes {
		if notificationType == supported {
			return true
		}
	}
	
	return false
}

// ValidatePriority validates if priority is supported
func ValidatePriority(priority string) bool {
	supportedPriorities := []string{
		PriorityLow,
		PriorityNormal,
		PriorityHigh,
		PriorityUrgent,
	}
	
	for _, supported := range supportedPriorities {
		if priority == supported {
			return true
		}
	}
	
	return false
}
