package repository

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"cmhk-platform/internal/user/model"
)

// UserRepository defines the interface for user data access
type UserRepository interface {
	Create(ctx context.Context, user *model.User) error
	GetByID(ctx context.Context, id uint64) (*model.User, error)
	GetByUsername(ctx context.Context, username string) (*model.User, error)
	GetByEmail(ctx context.Context, email string) (*model.User, error)
	GetByPhone(ctx context.Context, phone string) (*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id uint64) error
	List(ctx context.Context, query model.UserListQuery) ([]*model.User, int64, error)
	AssignRole(ctx context.Context, userID, roleID, grantedBy uint64) error
	RemoveRole(ctx context.Context, userID, roleID uint64) error
	GetUserRoles(ctx context.Context, userID uint64) ([]*model.Role, error)
	UpdateLastLogin(ctx context.Context, userID uint64) error
	IncrementFailedLoginCount(ctx context.Context, userID uint64) error
	ResetFailedLoginCount(ctx context.Context, userID uint64) error
	LockUser(ctx context.Context, userID uint64) error
	UnlockUser(ctx context.Context, userID uint64) error
}

// userRepository implements UserRepository interface
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

// Create creates a new user
func (r *userRepository) Create(ctx context.Context, user *model.User) error {
	if err := r.db.WithContext(ctx).Create(user).Error; err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}
	return nil
}

// GetByID retrieves a user by ID
func (r *userRepository) GetByID(ctx context.Context, id uint64) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).
		Preload("Roles").
		First(&user, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}

	return &user, nil
}

// GetByUsername retrieves a user by username
func (r *userRepository) GetByUsername(ctx context.Context, username string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).
		Preload("Roles").
		Where("username = ?", username).
		First(&user).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}

	return &user, nil
}

// GetByEmail retrieves a user by email
func (r *userRepository) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).
		Preload("Roles").
		Where("email = ?", email).
		First(&user).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	return &user, nil
}

// GetByPhone retrieves a user by phone
func (r *userRepository) GetByPhone(ctx context.Context, phone string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).
		Preload("Roles").
		Where("phone = ?", phone).
		First(&user).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to get user by phone: %w", err)
	}

	return &user, nil
}

// Update updates a user
func (r *userRepository) Update(ctx context.Context, user *model.User) error {
	if err := r.db.WithContext(ctx).Save(user).Error; err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}
	return nil
}

// Delete soft deletes a user
func (r *userRepository) Delete(ctx context.Context, id uint64) error {
	if err := r.db.WithContext(ctx).Delete(&model.User{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}
	return nil
}

// List retrieves users with pagination and filtering
func (r *userRepository) List(ctx context.Context, query model.UserListQuery) ([]*model.User, int64, error) {
	var users []*model.User
	var total int64

	db := r.db.WithContext(ctx).Model(&model.User{})

	// Apply filters
	if query.TenantID > 0 {
		db = db.Where("tenant_id = ?", query.TenantID)
	}

	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}

	if query.Search != "" {
		searchPattern := "%" + query.Search + "%"
		db = db.Where("username LIKE ? OR email LIKE ? OR phone LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Apply pagination
	offset := (query.Page - 1) * query.PageSize
	if err := db.Preload("Roles").
		Offset(offset).
		Limit(query.PageSize).
		Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", err)
	}

	return users, total, nil
}

// AssignRole assigns a role to a user
func (r *userRepository) AssignRole(ctx context.Context, userID, roleID, grantedBy uint64) error {
	userRole := &model.UserRole{
		UserID:    userID,
		RoleID:    roleID,
		GrantedBy: grantedBy,
	}

	if err := r.db.WithContext(ctx).Create(userRole).Error; err != nil {
		return fmt.Errorf("failed to assign role: %w", err)
	}

	return nil
}

// RemoveRole removes a role from a user
func (r *userRepository) RemoveRole(ctx context.Context, userID, roleID uint64) error {
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND role_id = ?", userID, roleID).
		Delete(&model.UserRole{}).Error; err != nil {
		return fmt.Errorf("failed to remove role: %w", err)
	}

	return nil
}

// GetUserRoles retrieves all roles for a user
func (r *userRepository) GetUserRoles(ctx context.Context, userID uint64) ([]*model.Role, error) {
	var roles []*model.Role

	err := r.db.WithContext(ctx).
		Table("roles").
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND roles.status = ?", userID, model.RoleStatusActive).
		Find(&roles).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	return roles, nil
}

// UpdateLastLogin updates the last login time for a user
func (r *userRepository) UpdateLastLogin(ctx context.Context, userID uint64) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Update("last_login_at", now).Error; err != nil {
		return fmt.Errorf("failed to update last login: %w", err)
	}

	return nil
}

// IncrementFailedLoginCount increments the failed login count for a user
func (r *userRepository) IncrementFailedLoginCount(ctx context.Context, userID uint64) error {
	if err := r.db.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Update("failed_login_count", gorm.Expr("failed_login_count + 1")).Error; err != nil {
		return fmt.Errorf("failed to increment failed login count: %w", err)
	}

	return nil
}

// ResetFailedLoginCount resets the failed login count for a user
func (r *userRepository) ResetFailedLoginCount(ctx context.Context, userID uint64) error {
	if err := r.db.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"failed_login_count": 0,
			"locked_until":       nil,
		}).Error; err != nil {
		return fmt.Errorf("failed to reset failed login count: %w", err)
	}

	return nil
}

// LockUser locks a user account
func (r *userRepository) LockUser(ctx context.Context, userID uint64) error {
	lockUntil := time.Now().Add(30 * time.Minute) // Lock for 30 minutes

	if err := r.db.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"status":      model.UserStatusLocked,
			"locked_until": lockUntil,
		}).Error; err != nil {
		return fmt.Errorf("failed to lock user: %w", err)
	}

	return nil
}

// UnlockUser unlocks a user account
func (r *userRepository) UnlockUser(ctx context.Context, userID uint64) error {
	if err := r.db.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"status":             model.UserStatusActive,
			"locked_until":       nil,
			"failed_login_count": 0,
		}).Error; err != nil {
		return fmt.Errorf("failed to unlock user: %w", err)
	}

	return nil
}
