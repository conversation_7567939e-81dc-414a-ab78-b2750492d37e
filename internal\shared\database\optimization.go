package database

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// OptimizationConfig holds database optimization settings
type OptimizationConfig struct {
	// Connection pool settings
	MaxOpenConns    int           `yaml:"max_open_conns" mapstructure:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns" mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime" mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time" mapstructure:"conn_max_idle_time"`

	// Query optimization
	PrepareStmt              bool          `yaml:"prepare_stmt" mapstructure:"prepare_stmt"`
	DisableForeignKeyConstraintWhenMigrating bool `yaml:"disable_foreign_key_constraint_when_migrating" mapstructure:"disable_foreign_key_constraint_when_migrating"`
	
	// Logging
	SlowThreshold time.Duration `yaml:"slow_threshold" mapstructure:"slow_threshold"`
	LogLevel      string        `yaml:"log_level" mapstructure:"log_level"`
	
	// Performance settings
	SkipDefaultTransaction bool `yaml:"skip_default_transaction" mapstructure:"skip_default_transaction"`
	FullSaveAssociations   bool `yaml:"full_save_associations" mapstructure:"full_save_associations"`
}

// DefaultOptimizationConfig returns default optimization settings
func DefaultOptimizationConfig() *OptimizationConfig {
	return &OptimizationConfig{
		MaxOpenConns:    100,
		MaxIdleConns:    10,
		ConnMaxLifetime: time.Hour,
		ConnMaxIdleTime: time.Minute * 10,
		PrepareStmt:     true,
		DisableForeignKeyConstraintWhenMigrating: false,
		SlowThreshold:   time.Millisecond * 200,
		LogLevel:        "warn",
		SkipDefaultTransaction: false,
		FullSaveAssociations:   false,
	}
}

// ApplyOptimizations applies optimization settings to the database connection
func ApplyOptimizations(db *gorm.DB, config *OptimizationConfig) error {
	// Get underlying sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}

	// Configure connection pool
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(config.ConnMaxIdleTime)

	// Configure GORM settings
	db.Config.PrepareStmt = config.PrepareStmt
	db.Config.DisableForeignKeyConstraintWhenMigrating = config.DisableForeignKeyConstraintWhenMigrating
	db.Config.SkipDefaultTransaction = config.SkipDefaultTransaction
	db.Config.FullSaveAssociations = config.FullSaveAssociations

	// Configure logger
	logLevel := logger.Warn
	switch config.LogLevel {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	}

	db.Logger = logger.Default.LogMode(logLevel).SlowThreshold(config.SlowThreshold)

	return nil
}

// QueryOptimizer provides query optimization utilities
type QueryOptimizer struct {
	db *gorm.DB
}

// NewQueryOptimizer creates a new query optimizer
func NewQueryOptimizer(db *gorm.DB) *QueryOptimizer {
	return &QueryOptimizer{db: db}
}

// WithPagination adds optimized pagination to a query
func (q *QueryOptimizer) WithPagination(query *gorm.DB, page, pageSize int) *gorm.DB {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	return query.Offset(offset).Limit(pageSize)
}

// WithSelectFields adds optimized field selection to avoid N+1 queries
func (q *QueryOptimizer) WithSelectFields(query *gorm.DB, fields []string) *gorm.DB {
	if len(fields) > 0 {
		return query.Select(fields)
	}
	return query
}

// WithPreload adds optimized preloading to avoid N+1 queries
func (q *QueryOptimizer) WithPreload(query *gorm.DB, associations []string) *gorm.DB {
	for _, assoc := range associations {
		query = query.Preload(assoc)
	}
	return query
}

// WithIndex suggests using specific index for the query
func (q *QueryOptimizer) WithIndex(query *gorm.DB, indexName string) *gorm.DB {
	return query.Set("gorm:query_hint", "USE INDEX ("+indexName+")")
}

// BatchInsert performs optimized batch insert
func (q *QueryOptimizer) BatchInsert(data interface{}, batchSize int) error {
	if batchSize <= 0 {
		batchSize = 100
	}
	return q.db.CreateInBatches(data, batchSize).Error
}

// BatchUpdate performs optimized batch update
func (q *QueryOptimizer) BatchUpdate(model interface{}, updates map[string]interface{}, where string, args ...interface{}) error {
	return q.db.Model(model).Where(where, args...).Updates(updates).Error
}

// ExplainQuery returns the execution plan for a query (MySQL specific)
func (q *QueryOptimizer) ExplainQuery(query *gorm.DB) ([]map[string]interface{}, error) {
	var results []map[string]interface{}
	
	// Get the SQL and args from the query
	sql := q.db.ToSQL(func(tx *gorm.DB) *gorm.DB {
		return query
	})
	
	// Execute EXPLAIN
	err := q.db.Raw("EXPLAIN " + sql).Scan(&results).Error
	return results, err
}

// IndexSuggestion represents an index suggestion
type IndexSuggestion struct {
	Table   string   `json:"table"`
	Columns []string `json:"columns"`
	Type    string   `json:"type"` // BTREE, HASH, etc.
	Reason  string   `json:"reason"`
}

// AnalyzeSlowQueries analyzes slow queries and suggests optimizations
func (q *QueryOptimizer) AnalyzeSlowQueries() ([]IndexSuggestion, error) {
	var suggestions []IndexSuggestion
	
	// This is a simplified example - in production, you would analyze
	// slow query logs and suggest indexes based on actual query patterns
	
	// Example suggestions based on common patterns
	suggestions = append(suggestions, IndexSuggestion{
		Table:   "users",
		Columns: []string{"tenant_id", "status"},
		Type:    "BTREE",
		Reason:  "Frequently used in WHERE clauses for user listing",
	})
	
	suggestions = append(suggestions, IndexSuggestion{
		Table:   "messages",
		Columns: []string{"tenant_id", "status", "created_at"},
		Type:    "BTREE",
		Reason:  "Composite index for message filtering and sorting",
	})
	
	suggestions = append(suggestions, IndexSuggestion{
		Table:   "customers",
		Columns: []string{"email"},
		Type:    "BTREE",
		Reason:  "Unique constraint and frequent lookups",
	})
	
	return suggestions, nil
}

// CacheStrategy defines caching strategy for queries
type CacheStrategy struct {
	TTL        time.Duration
	KeyPattern string
	Enabled    bool
}

// GetCacheStrategy returns appropriate cache strategy for different query types
func GetCacheStrategy(queryType string) CacheStrategy {
	strategies := map[string]CacheStrategy{
		"user_profile": {
			TTL:        time.Minute * 15,
			KeyPattern: "user:profile:%d",
			Enabled:    true,
		},
		"user_permissions": {
			TTL:        time.Minute * 30,
			KeyPattern: "user:permissions:%d",
			Enabled:    true,
		},
		"system_config": {
			TTL:        time.Hour,
			KeyPattern: "config:%s",
			Enabled:    true,
		},
		"message_stats": {
			TTL:        time.Minute * 5,
			KeyPattern: "stats:messages:%d",
			Enabled:    true,
		},
		"customer_stats": {
			TTL:        time.Minute * 10,
			KeyPattern: "stats:customers:%d",
			Enabled:    true,
		},
	}
	
	if strategy, exists := strategies[queryType]; exists {
		return strategy
	}
	
	// Default strategy
	return CacheStrategy{
		TTL:        time.Minute * 5,
		KeyPattern: "default:%s",
		Enabled:    false,
	}
}

// PerformanceMetrics holds database performance metrics
type PerformanceMetrics struct {
	ConnectionsActive int           `json:"connections_active"`
	ConnectionsIdle   int           `json:"connections_idle"`
	QueriesPerSecond  float64       `json:"queries_per_second"`
	AverageQueryTime  time.Duration `json:"average_query_time"`
	SlowQueries       int64         `json:"slow_queries"`
	CacheHitRatio     float64       `json:"cache_hit_ratio"`
}

// GetPerformanceMetrics returns current database performance metrics
func (q *QueryOptimizer) GetPerformanceMetrics() (*PerformanceMetrics, error) {
	sqlDB, err := q.db.DB()
	if err != nil {
		return nil, err
	}
	
	stats := sqlDB.Stats()
	
	metrics := &PerformanceMetrics{
		ConnectionsActive: stats.OpenConnections,
		ConnectionsIdle:   stats.Idle,
		// These would be calculated from actual monitoring data
		QueriesPerSecond: 0,
		AverageQueryTime: 0,
		SlowQueries:      0,
		CacheHitRatio:    0,
	}
	
	return metrics, nil
}
