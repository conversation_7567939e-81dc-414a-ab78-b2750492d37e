package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"cmhk-platform/internal/communication/model"
	"cmhk-platform/internal/communication/service"
	"cmhk-platform/internal/shared/auth"
	"cmhk-platform/internal/shared/types"
	"cmhk-platform/pkg/logger"
)

// MessageHandler handles message-related HTTP requests
type MessageHandler struct {
	messageService service.MessageService
	logger         *logger.Logger
}

// NewMessageHandler creates a new message handler
func NewMessageHandler(messageService service.MessageService, logger *logger.Logger) *MessageHandler {
	return &MessageHandler{
		messageService: messageService,
		logger:         logger,
	}
}

// SendMessage handles message sending requests
func (h *MessageHandler) SendMessage(c *gin.Context) {
	var req model.SendMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	// Get current tenant ID from context
	if tenantID, ok := auth.GetCurrentTenantID(c); ok {
		req.TenantID = tenantID
	}

	message, err := h.messageService.SendMessage(c.Request.Context(), req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Message sent successfully", message.ToMessageResponse())
}

// SendBulkMessage handles bulk message sending requests
func (h *MessageHandler) SendBulkMessage(c *gin.Context) {
	var req model.SendBulkMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	// Get current tenant ID from context
	if tenantID, ok := auth.GetCurrentTenantID(c); ok {
		req.TenantID = tenantID
	}

	messages, err := h.messageService.SendBulkMessage(c.Request.Context(), req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	// Convert to response DTOs
	responses := make([]model.MessageResponse, len(messages))
	for i, message := range messages {
		responses[i] = message.ToMessageResponse()
	}

	types.SuccessResponseWithMessage(c, "Bulk messages sent successfully", responses)
}

// GetMessage handles get message by ID requests
func (h *MessageHandler) GetMessage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid message ID")
		return
	}

	message, err := h.messageService.GetMessage(c.Request.Context(), id)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponse(c, message.ToMessageResponse())
}

// ListMessages handles message listing requests
func (h *MessageHandler) ListMessages(c *gin.Context) {
	var query model.MessageListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	// Get current tenant ID from context
	if tenantID, ok := auth.GetCurrentTenantID(c); ok {
		query.TenantID = tenantID
	}

	messages, total, err := h.messageService.ListMessages(c.Request.Context(), query)
	if err != nil {
		h.handleError(c, err)
		return
	}

	// Convert to response DTOs
	responses := make([]model.MessageResponse, len(messages))
	for i, message := range messages {
		responses[i] = message.ToMessageResponse()
	}

	pagination := types.NewPagination(query.Page, query.PageSize, total)
	types.PaginatedSuccessResponse(c, responses, pagination)
}

// UpdateMessageStatus handles message status update requests
func (h *MessageHandler) UpdateMessageStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid message ID")
		return
	}

	var req model.MessageStatusUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	if err := h.messageService.UpdateMessageStatus(c.Request.Context(), id, req); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Message status updated successfully", nil)
}

// RetryMessage handles message retry requests
func (h *MessageHandler) RetryMessage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid message ID")
		return
	}

	if err := h.messageService.RetryMessage(c.Request.Context(), id); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Message retry initiated successfully", nil)
}

// GetMessageStats handles message statistics requests
func (h *MessageHandler) GetMessageStats(c *gin.Context) {
	tenantID, ok := auth.GetCurrentTenantID(c)
	if !ok {
		types.ErrorResponse(c, http.StatusUnauthorized, types.ErrorCodeUnauthorized, "Tenant not found")
		return
	}

	stats, err := h.messageService.GetMessageStats(c.Request.Context(), tenantID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponse(c, stats)
}

// handleError handles service errors and converts them to appropriate HTTP responses
func (h *MessageHandler) handleError(c *gin.Context, err error) {
	h.logger.Error("Message handler error", zap.Error(err))

	switch err {
	case model.ErrMessageNotFound:
		types.ErrorResponse(c, http.StatusNotFound, model.GetErrorCode(err), err.Error())
	case model.ErrInvalidChannel:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrInvalidMessageType:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrInvalidRecipient:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrContentOrTemplateRequired:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrMessageAlreadySent:
		types.ErrorResponse(c, http.StatusConflict, model.GetErrorCode(err), err.Error())
	case model.ErrMessageCannotBeRetried:
		types.ErrorResponse(c, http.StatusConflict, model.GetErrorCode(err), err.Error())
	case model.ErrChannelNotConfigured:
		types.ErrorResponse(c, http.StatusServiceUnavailable, model.GetErrorCode(err), err.Error())
	case model.ErrChannelDisabled:
		types.ErrorResponse(c, http.StatusServiceUnavailable, model.GetErrorCode(err), err.Error())
	case model.ErrRateLimitExceeded:
		types.ErrorResponse(c, http.StatusTooManyRequests, model.GetErrorCode(err), err.Error())
	case model.ErrTemplateNotFound:
		types.ErrorResponse(c, http.StatusNotFound, model.GetErrorCode(err), err.Error())
	case model.ErrTemplateInactive:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrExternalServiceUnavailable:
		types.ErrorResponse(c, http.StatusServiceUnavailable, model.GetErrorCode(err), err.Error())
	default:
		types.ErrorResponse(c, http.StatusInternalServerError, types.ErrorCodeInternalError, "Internal server error")
	}
}
