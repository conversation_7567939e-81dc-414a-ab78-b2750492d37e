package repository

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"cmhk-platform/internal/customer/model"
)

// CustomerGroupRepository defines the interface for customer group data access
type CustomerGroupRepository interface {
	Create(ctx context.Context, group *model.CustomerGroup) error
	GetByID(ctx context.Context, id uint64) (*model.CustomerGroup, error)
	GetByCode(ctx context.Context, tenantID uint64, code string) (*model.CustomerGroup, error)
	Update(ctx context.Context, group *model.CustomerGroup) error
	Delete(ctx context.Context, id uint64) error
	List(ctx context.Context, query model.CustomerGroupListQuery) ([]*model.CustomerGroup, int64, error)
	GetMemberCount(ctx context.Context, groupID uint64) (int64, error)
	HasMembers(ctx context.Context, groupID uint64) (bool, error)
	AddMembers(ctx context.Context, groupID uint64, customerIDs []uint64) error
	RemoveMembers(ctx context.Context, groupID uint64, customerIDs []uint64) error
}

// customerGroupRepository implements CustomerGroupRepository interface
type customerGroupRepository struct {
	db *gorm.DB
}

// NewCustomerGroupRepository creates a new customer group repository
func NewCustomerGroupRepository(db *gorm.DB) CustomerGroupRepository {
	return &customerGroupRepository{db: db}
}

// Create creates a new customer group
func (r *customerGroupRepository) Create(ctx context.Context, group *model.CustomerGroup) error {
	if err := r.db.WithContext(ctx).Create(group).Error; err != nil {
		return fmt.Errorf("failed to create customer group: %w", err)
	}
	return nil
}

// GetByID retrieves a customer group by ID
func (r *customerGroupRepository) GetByID(ctx context.Context, id uint64) (*model.CustomerGroup, error) {
	var group model.CustomerGroup
	err := r.db.WithContext(ctx).First(&group, id).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrGroupNotFound
		}
		return nil, fmt.Errorf("failed to get customer group by ID: %w", err)
	}
	
	return &group, nil
}

// GetByCode retrieves a customer group by tenant ID and group code
func (r *customerGroupRepository) GetByCode(ctx context.Context, tenantID uint64, code string) (*model.CustomerGroup, error) {
	var group model.CustomerGroup
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND group_code = ?", tenantID, code).
		First(&group).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrGroupNotFound
		}
		return nil, fmt.Errorf("failed to get customer group by code: %w", err)
	}
	
	return &group, nil
}

// Update updates a customer group
func (r *customerGroupRepository) Update(ctx context.Context, group *model.CustomerGroup) error {
	if err := r.db.WithContext(ctx).Save(group).Error; err != nil {
		return fmt.Errorf("failed to update customer group: %w", err)
	}
	return nil
}

// Delete soft deletes a customer group
func (r *customerGroupRepository) Delete(ctx context.Context, id uint64) error {
	if err := r.db.WithContext(ctx).Delete(&model.CustomerGroup{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete customer group: %w", err)
	}
	return nil
}

// List retrieves customer groups with pagination and filtering
func (r *customerGroupRepository) List(ctx context.Context, query model.CustomerGroupListQuery) ([]*model.CustomerGroup, int64, error) {
	var groups []*model.CustomerGroup
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.CustomerGroup{})
	
	// Apply filters
	if query.TenantID > 0 {
		db = db.Where("tenant_id = ?", query.TenantID)
	}
	
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}
	
	if query.Search != "" {
		searchPattern := "%" + query.Search + "%"
		db = db.Where("group_name LIKE ? OR group_code LIKE ? OR description LIKE ?", 
			searchPattern, searchPattern, searchPattern)
	}
	
	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customer groups: %w", err)
	}
	
	// Apply pagination
	offset := (query.Page - 1) * query.PageSize
	if err := db.Offset(offset).
		Limit(query.PageSize).
		Order("created_at DESC").
		Find(&groups).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list customer groups: %w", err)
	}
	
	return groups, total, nil
}

// GetMemberCount returns the number of members in a group
func (r *customerGroupRepository) GetMemberCount(ctx context.Context, groupID uint64) (int64, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&model.CustomerGroupMember{}).
		Where("group_id = ?", groupID).
		Count(&count).Error
	
	if err != nil {
		return 0, fmt.Errorf("failed to get group member count: %w", err)
	}
	
	return count, nil
}

// HasMembers checks if a group has members
func (r *customerGroupRepository) HasMembers(ctx context.Context, groupID uint64) (bool, error) {
	count, err := r.GetMemberCount(ctx, groupID)
	if err != nil {
		return false, err
	}
	
	return count > 0, nil
}

// AddMembers adds multiple customers to a group
func (r *customerGroupRepository) AddMembers(ctx context.Context, groupID uint64, customerIDs []uint64) error {
	if len(customerIDs) == 0 {
		return nil
	}
	
	members := make([]*model.CustomerGroupMember, len(customerIDs))
	for i, customerID := range customerIDs {
		members[i] = &model.CustomerGroupMember{
			CustomerID: customerID,
			GroupID:    groupID,
		}
	}
	
	if err := r.db.WithContext(ctx).CreateInBatches(members, 100).Error; err != nil {
		return fmt.Errorf("failed to add members to group: %w", err)
	}
	
	return nil
}

// RemoveMembers removes multiple customers from a group
func (r *customerGroupRepository) RemoveMembers(ctx context.Context, groupID uint64, customerIDs []uint64) error {
	if len(customerIDs) == 0 {
		return nil
	}
	
	if err := r.db.WithContext(ctx).
		Where("group_id = ? AND customer_id IN (?)", groupID, customerIDs).
		Delete(&model.CustomerGroupMember{}).Error; err != nil {
		return fmt.Errorf("failed to remove members from group: %w", err)
	}
	
	return nil
}
