package model

import "errors"

// Campaign related errors
var (
	ErrCampaignNotFound        = errors.New("campaign not found")
	ErrCampaignAlreadyExists   = errors.New("campaign already exists")
	ErrCampaignNotEditable     = errors.New("campaign is not editable")
	ErrCampaignNotStartable    = errors.New("campaign cannot be started")
	ErrCampaignNotPausable     = errors.New("campaign cannot be paused")
	ErrCampaignNotResumable    = errors.New("campaign cannot be resumed")
	ErrCampaignNotCancellable  = errors.New("campaign cannot be cancelled")
	ErrInvalidCampaignType     = errors.New("invalid campaign type")
	ErrInvalidCampaignStatus   = errors.New("invalid campaign status")
	ErrInvalidTimeRange        = errors.New("invalid time range")
	ErrCampaignExpired         = errors.New("campaign has expired")
	ErrInsufficientBudget      = errors.New("insufficient budget")
)

// Execution related errors
var (
	ErrExecutionNotFound       = errors.New("campaign execution not found")
	ErrExecutionNotRetryable   = errors.New("execution cannot be retried")
	ErrInvalidExecutionType    = errors.New("invalid execution type")
	ErrInvalidExecutionStatus  = errors.New("invalid execution status")
	ErrScheduledTimeRequired   = errors.New("scheduled time is required")
	ErrExecutionAlreadyRunning = errors.New("execution is already running")
	ErrNoTargetAudience        = errors.New("no target audience defined")
)

// Analytics related errors
var (
	ErrAnalyticsNotFound    = errors.New("analytics data not found")
	ErrInvalidMetricName    = errors.New("invalid metric name")
	ErrInvalidMetricValue   = errors.New("invalid metric value")
	ErrInvalidDateRange     = errors.New("invalid date range")
)

// Error codes for API responses
const (
	ErrorCodeCampaignNotFound        = "CAMPAIGN_NOT_FOUND"
	ErrorCodeCampaignAlreadyExists   = "CAMPAIGN_ALREADY_EXISTS"
	ErrorCodeCampaignNotEditable     = "CAMPAIGN_NOT_EDITABLE"
	ErrorCodeCampaignNotStartable    = "CAMPAIGN_NOT_STARTABLE"
	ErrorCodeCampaignNotPausable     = "CAMPAIGN_NOT_PAUSABLE"
	ErrorCodeCampaignNotResumable    = "CAMPAIGN_NOT_RESUMABLE"
	ErrorCodeCampaignNotCancellable  = "CAMPAIGN_NOT_CANCELLABLE"
	ErrorCodeInvalidCampaignType     = "INVALID_CAMPAIGN_TYPE"
	ErrorCodeInvalidCampaignStatus   = "INVALID_CAMPAIGN_STATUS"
	ErrorCodeInvalidTimeRange        = "INVALID_TIME_RANGE"
	ErrorCodeCampaignExpired         = "CAMPAIGN_EXPIRED"
	ErrorCodeInsufficientBudget      = "INSUFFICIENT_BUDGET"
	ErrorCodeExecutionNotFound       = "EXECUTION_NOT_FOUND"
	ErrorCodeExecutionNotRetryable   = "EXECUTION_NOT_RETRYABLE"
	ErrorCodeInvalidExecutionType    = "INVALID_EXECUTION_TYPE"
	ErrorCodeInvalidExecutionStatus  = "INVALID_EXECUTION_STATUS"
	ErrorCodeScheduledTimeRequired   = "SCHEDULED_TIME_REQUIRED"
	ErrorCodeExecutionAlreadyRunning = "EXECUTION_ALREADY_RUNNING"
	ErrorCodeNoTargetAudience        = "NO_TARGET_AUDIENCE"
	ErrorCodeAnalyticsNotFound       = "ANALYTICS_NOT_FOUND"
	ErrorCodeInvalidMetricName       = "INVALID_METRIC_NAME"
	ErrorCodeInvalidMetricValue      = "INVALID_METRIC_VALUE"
	ErrorCodeInvalidDateRange        = "INVALID_DATE_RANGE"
)

// GetErrorCode returns the error code for a given error
func GetErrorCode(err error) string {
	switch err {
	case ErrCampaignNotFound:
		return ErrorCodeCampaignNotFound
	case ErrCampaignAlreadyExists:
		return ErrorCodeCampaignAlreadyExists
	case ErrCampaignNotEditable:
		return ErrorCodeCampaignNotEditable
	case ErrCampaignNotStartable:
		return ErrorCodeCampaignNotStartable
	case ErrCampaignNotPausable:
		return ErrorCodeCampaignNotPausable
	case ErrCampaignNotResumable:
		return ErrorCodeCampaignNotResumable
	case ErrCampaignNotCancellable:
		return ErrorCodeCampaignNotCancellable
	case ErrInvalidCampaignType:
		return ErrorCodeInvalidCampaignType
	case ErrInvalidCampaignStatus:
		return ErrorCodeInvalidCampaignStatus
	case ErrInvalidTimeRange:
		return ErrorCodeInvalidTimeRange
	case ErrCampaignExpired:
		return ErrorCodeCampaignExpired
	case ErrInsufficientBudget:
		return ErrorCodeInsufficientBudget
	case ErrExecutionNotFound:
		return ErrorCodeExecutionNotFound
	case ErrExecutionNotRetryable:
		return ErrorCodeExecutionNotRetryable
	case ErrInvalidExecutionType:
		return ErrorCodeInvalidExecutionType
	case ErrInvalidExecutionStatus:
		return ErrorCodeInvalidExecutionStatus
	case ErrScheduledTimeRequired:
		return ErrorCodeScheduledTimeRequired
	case ErrExecutionAlreadyRunning:
		return ErrorCodeExecutionAlreadyRunning
	case ErrNoTargetAudience:
		return ErrorCodeNoTargetAudience
	case ErrAnalyticsNotFound:
		return ErrorCodeAnalyticsNotFound
	case ErrInvalidMetricName:
		return ErrorCodeInvalidMetricName
	case ErrInvalidMetricValue:
		return ErrorCodeInvalidMetricValue
	case ErrInvalidDateRange:
		return ErrorCodeInvalidDateRange
	default:
		return "UNKNOWN_ERROR"
	}
}
