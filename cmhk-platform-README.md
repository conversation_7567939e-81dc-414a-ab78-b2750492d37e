# CMHK通信能力开放平台 - Golang单体应用

## 项目概述

CMHK通信能力开放平台是一个基于Golang的单体应用，提供全渠道通信能力、客户数据平台、AI驱动的智能化服务和营销活动管理等功能。

## 技术栈

- **Web框架**: Gin
- **ORM框架**: GORM
- **配置管理**: Viper
- **缓存**: go-redis
- **日志**: Zap
- **监控**: Prometheus
- **数据库**: MySQL 8.0, Redis 7.0, ClickHouse

## 项目结构

```
cmhk-platform/
├── cmd/server/          # 应用程序入口
├── internal/            # 私有应用代码
│   ├── user/           # 用户模块
│   ├── communication/  # 通信模块
│   ├── customer/       # 客户模块
│   ├── marketing/      # 营销模块
│   ├── analytics/      # 分析模块
│   ├── platform/       # 平台模块
│   ├── file/           # 文件模块
│   ├── workflow/       # 工作流模块
│   ├── shared/         # 共享组件
│   └── api/            # API路由
├── pkg/                # 公共库
├── configs/            # 配置文件
└── scripts/            # 构建脚本
```

## 快速开始

### 1. 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 7.0+
- ClickHouse (可选)

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 配置

复制并修改配置文件：
```bash
cp configs/config.yaml configs/config-local.yaml
# 修改配置文件中的数据库连接信息
```

### 4. 运行

```bash
# 开发模式
go run cmd/server/main.go

# 或使用构建脚本
./scripts/build.sh
./bin/cmhk-platform
```

## 开发指南

### 模块结构

每个业务模块遵循以下分层架构：
- `handler/` - HTTP请求处理器
- `service/` - 业务逻辑层
- `repository/` - 数据访问层
- `model/` - 数据模型

### 添加新功能

1. 在对应模块下创建相应的文件
2. 实现接口定义
3. 在路由中注册新的端点
4. 编写单元测试

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t cmhk-platform .

# 运行容器
docker run -p 8080:8080 cmhk-platform
```

### Kubernetes部署

```bash
kubectl apply -f deployments/kubernetes/
```

## 监控

- 健康检查: `GET /health`
- 指标监控: `GET /metrics`
- API文档: `GET /swagger/`

## 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

Copyright © 2024 CMHK. All rights reserved.
