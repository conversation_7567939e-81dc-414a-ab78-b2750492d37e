package model

import (
	"time"
	"gorm.io/gorm"
)

// Message represents a message in the system
type Message struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_status" json:"tenant_id"`
	MessageType string         `gorm:"size:20;not null" json:"message_type"`
	Channel     string         `gorm:"size:20;not null;index:idx_channel_status" json:"channel"`
	Sender      string         `gorm:"size:100" json:"sender"`
	Recipient   string         `gorm:"size:100;not null" json:"recipient"`
	Subject     string         `gorm:"size:200" json:"subject"`
	Content     string         `gorm:"type:text;not null" json:"content"`
	TemplateID  *uint64        `json:"template_id"`
	Status      string         `gorm:"size:20;default:pending;index:idx_tenant_status,idx_channel_status" json:"status"`
	SentAt      *time.Time     `json:"sent_at"`
	DeliveredAt *time.Time     `json:"delivered_at"`
	ReadAt      *time.Time     `json:"read_at"`
	ErrorMessage string        `gorm:"type:text" json:"error_message"`
	Metadata    string         `gorm:"type:json" json:"metadata"`
	CreatedAt   time.Time      `gorm:"index:idx_created_at" json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Template *Template `gorm:"foreignKey:TemplateID" json:"template,omitempty"`
}

// Template represents a message template
type Template struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;uniqueIndex:uk_tenant_code" json:"tenant_id"`
	TemplateCode string        `gorm:"size:50;not null;uniqueIndex:uk_tenant_code" json:"template_code"`
	TemplateName string        `gorm:"size:100;not null" json:"template_name"`
	Channel     string         `gorm:"size:20;not null;index:idx_channel" json:"channel"`
	Subject     string         `gorm:"size:200" json:"subject"`
	Content     string         `gorm:"type:text;not null" json:"content"`
	Variables   string         `gorm:"type:json" json:"variables"`
	Status      int8           `gorm:"default:1" json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// Message types
const (
	MessageTypeTransactional = "transactional"
	MessageTypeMarketing     = "marketing"
	MessageTypeNotification  = "notification"
	MessageTypeAlert         = "alert"
)

// Message channels
const (
	ChannelSMS      = "sms"
	ChannelEmail    = "email"
	ChannelWhatsApp = "whatsapp"
	ChannelWeChat   = "wechat"
	ChannelPush     = "push"
	ChannelWebhook  = "webhook"
)

// Message statuses
const (
	StatusPending   = "pending"
	StatusSending   = "sending"
	StatusSent      = "sent"
	StatusDelivered = "delivered"
	StatusRead      = "read"
	StatusFailed    = "failed"
	StatusCancelled = "cancelled"
)

// Template statuses
const (
	TemplateStatusInactive = 0
	TemplateStatusActive   = 1
)

// TableName returns the table name for Message
func (Message) TableName() string {
	return "messages"
}

// TableName returns the table name for Template
func (Template) TableName() string {
	return "templates"
}

// IsDelivered checks if message is delivered
func (m *Message) IsDelivered() bool {
	return m.Status == StatusDelivered || m.Status == StatusRead
}

// IsFailed checks if message failed
func (m *Message) IsFailed() bool {
	return m.Status == StatusFailed
}

// CanRetry checks if message can be retried
func (m *Message) CanRetry() bool {
	return m.Status == StatusFailed || m.Status == StatusPending
}

// IsActive checks if template is active
func (t *Template) IsActive() bool {
	return t.Status == TemplateStatusActive
}

// GetVariableNames returns variable names from template
func (t *Template) GetVariableNames() []string {
	// This would parse the Variables JSON field
	// For now, return empty slice
	return []string{}
}

// ValidateChannel validates if channel is supported
func ValidateChannel(channel string) bool {
	supportedChannels := []string{
		ChannelSMS,
		ChannelEmail,
		ChannelWhatsApp,
		ChannelWeChat,
		ChannelPush,
		ChannelWebhook,
	}
	
	for _, supported := range supportedChannels {
		if channel == supported {
			return true
		}
	}
	
	return false
}

// ValidateMessageType validates if message type is supported
func ValidateMessageType(messageType string) bool {
	supportedTypes := []string{
		MessageTypeTransactional,
		MessageTypeMarketing,
		MessageTypeNotification,
		MessageTypeAlert,
	}
	
	for _, supported := range supportedTypes {
		if messageType == supported {
			return true
		}
	}
	
	return false
}
