# 通信服务实现示例

## 1. 通信模型 (internal/communication/model/message.go)

```go
package model

import (
    "time"
    "gorm.io/gorm"
)

type MessageRecord struct {
    ID           uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
    MessageID    string         `gorm:"uniqueIndex;size:64;not null" json:"message_id"`
    TenantID     uint64         `gorm:"not null;index:idx_tenant_customer" json:"tenant_id"`
    CustomerID   string         `gorm:"size:50;index:idx_tenant_customer" json:"customer_id"`
    Channel      string         `gorm:"size:20;not null;index:idx_channel_status" json:"channel"`
    Sender       string         `gorm:"size:100" json:"sender"`
    Receiver     string         `gorm:"size:100;not null" json:"receiver"`
    Content      string         `gorm:"type:text" json:"content"`
    TemplateID   uint64         `gorm:"index" json:"template_id"`
    CampaignID   uint64         `gorm:"index" json:"campaign_id"`
    Status       string         `gorm:"size:20;default:'PENDING';index:idx_channel_status" json:"status"`
    SentAt       *time.Time     `gorm:"index:idx_status_sent" json:"sent_at"`
    DeliveredAt  *time.Time     `json:"delivered_at"`
    FailedReason string         `gorm:"type:text" json:"failed_reason"`
    Cost         float64        `gorm:"type:decimal(10,4);default:0" json:"cost"`
    CreatedAt    time.Time      `gorm:"index" json:"created_at"`
    UpdatedAt    time.Time      `json:"updated_at"`
    DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

type MessageTemplate struct {
    ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
    TenantID    uint64         `gorm:"not null;uniqueIndex:uk_tenant_code" json:"tenant_id"`
    TemplateCode string        `gorm:"size:50;not null;uniqueIndex:uk_tenant_code" json:"template_code"`
    TemplateName string        `gorm:"size:200;not null" json:"template_name"`
    Channel     string         `gorm:"size:20;not null;index:idx_tenant_channel" json:"channel"`
    Content     string         `gorm:"type:text;not null" json:"content"`
    Variables   string         `gorm:"type:json" json:"variables"`
    Status      int8           `gorm:"default:1;index" json:"status"`
    CreatedBy   uint64         `json:"created_by"`
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// DTO结构
type SendMessageRequest struct {
    Channel    string            `json:"channel" binding:"required,oneof=SMS WHATSAPP WECHAT EMAIL"`
    Receiver   string            `json:"receiver" binding:"required"`
    Content    string            `json:"content" binding:"required"`
    TemplateID uint64            `json:"template_id"`
    Variables  map[string]string `json:"variables"`
    CampaignID uint64            `json:"campaign_id"`
    Sender     string            `json:"sender"`
}

type BatchMessageRequest struct {
    Channel    string                   `json:"channel" binding:"required"`
    Messages   []SingleMessageRequest   `json:"messages" binding:"required,min=1,max=1000"`
    TemplateID uint64                   `json:"template_id"`
    CampaignID uint64                   `json:"campaign_id"`
}

type SingleMessageRequest struct {
    Receiver  string            `json:"receiver" binding:"required"`
    Variables map[string]string `json:"variables"`
}

type MessageResponse struct {
    MessageID         string    `json:"message_id"`
    Status            string    `json:"status"`
    EstimatedDelivery time.Time `json:"estimated_delivery"`
    Cost              float64   `json:"cost"`
}

type BatchMessageResponse struct {
    BatchID      string            `json:"batch_id"`
    TotalCount   int               `json:"total_count"`
    SuccessCount int               `json:"success_count"`
    FailedCount  int               `json:"failed_count"`
    Messages     []MessageResponse `json:"messages"`
}

type MessageStatusResponse struct {
    MessageID    string     `json:"message_id"`
    Status       string     `json:"status"`
    SentAt       *time.Time `json:"sent_at"`
    DeliveredAt  *time.Time `json:"delivered_at"`
    FailedReason string     `json:"failed_reason"`
    Cost         float64    `json:"cost"`
}

type CreateTemplateRequest struct {
    TemplateCode string   `json:"template_code" binding:"required,min=1,max=50"`
    TemplateName string   `json:"template_name" binding:"required,min=1,max=200"`
    Channel      string   `json:"channel" binding:"required,oneof=SMS WHATSAPP WECHAT EMAIL"`
    Content      string   `json:"content" binding:"required"`
    Variables    []string `json:"variables"`
}

type UpdateTemplateRequest struct {
    TemplateName string   `json:"template_name" binding:"omitempty,min=1,max=200"`
    Content      string   `json:"content" binding:"omitempty"`
    Variables    []string `json:"variables"`
    Status       int8     `json:"status" binding:"omitempty,oneof=0 1"`
}
```

## 2. 渠道处理器接口 (internal/communication/channels/handler.go)

```go
package channels

import (
    "context"
    "fmt"
    "regexp"
    "time"
    "math/rand"
    "cmhk-platform/internal/communication/model"
)

type ChannelHandler interface {
    Supports(channel string) bool
    SendMessage(ctx context.Context, req *model.SendMessageRequest) (*MessageResult, error)
    GetMessageStatus(ctx context.Context, messageID string) (*MessageStatus, error)
    ValidateReceiver(receiver string) error
}

type MessageResult struct {
    MessageID string
    Status    string
    Cost      float64
    ExternalID string
}

type MessageStatus struct {
    Status       string
    DeliveredAt  *time.Time
    FailedReason string
}

// SMS渠道处理器
type SMSHandler struct {
    gateway string
    apiKey  string
    secret  string
}

func NewSMSHandler(gateway, apiKey, secret string) *SMSHandler {
    return &SMSHandler{
        gateway: gateway,
        apiKey:  apiKey,
        secret:  secret,
    }
}

func (h *SMSHandler) Supports(channel string) bool {
    return channel == "SMS"
}

func (h *SMSHandler) SendMessage(ctx context.Context, req *model.SendMessageRequest) (*MessageResult, error) {
    // 验证手机号格式
    if err := h.ValidateReceiver(req.Receiver); err != nil {
        return nil, err
    }

    // 调用SMS网关API
    result, err := h.callSMSGateway(ctx, req)
    if err != nil {
        return nil, fmt.Errorf("failed to send SMS: %w", err)
    }

    return &MessageResult{
        MessageID:  result.MessageID,
        Status:     "SENT",
        Cost:       h.calculateCost(req.Content),
        ExternalID: result.ExternalID,
    }, nil
}

func (h *SMSHandler) ValidateReceiver(receiver string) error {
    // 手机号格式验证
    phoneRegex := regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
    if !phoneRegex.MatchString(receiver) {
        return fmt.Errorf("invalid phone number format: %s", receiver)
    }
    return nil
}

func (h *SMSHandler) GetMessageStatus(ctx context.Context, messageID string) (*MessageStatus, error) {
    // 实际实现中会调用SMS网关查询状态
    return &MessageStatus{
        Status: "DELIVERED",
        DeliveredAt: &time.Time{},
    }, nil
}

func (h *SMSHandler) calculateCost(content string) float64 {
    // 按字符数计算费用
    length := len([]rune(content))
    segments := (length + 159) / 160 // 每160字符一条
    return float64(segments) * 0.05  // 每条0.05元
}

func (h *SMSHandler) callSMSGateway(ctx context.Context, req *model.SendMessageRequest) (*SMSGatewayResult, error) {
    // 实际的SMS网关调用逻辑
    // 这里是示例实现
    return &SMSGatewayResult{
        MessageID:  generateMessageID(),
        ExternalID: "sms_" + generateMessageID(),
        Status:     "sent",
    }, nil
}

type SMSGatewayResult struct {
    MessageID  string
    ExternalID string
    Status     string
}

// WhatsApp渠道处理器
type WhatsAppHandler struct {
    businessAPIURL string
    accessToken    string
    phoneNumberID  string
}

func NewWhatsAppHandler(businessAPIURL, accessToken, phoneNumberID string) *WhatsAppHandler {
    return &WhatsAppHandler{
        businessAPIURL: businessAPIURL,
        accessToken:    accessToken,
        phoneNumberID:  phoneNumberID,
    }
}

func (h *WhatsAppHandler) Supports(channel string) bool {
    return channel == "WHATSAPP"
}

func (h *WhatsAppHandler) SendMessage(ctx context.Context, req *model.SendMessageRequest) (*MessageResult, error) {
    // 验证WhatsApp号码格式
    if err := h.ValidateReceiver(req.Receiver); err != nil {
        return nil, err
    }

    // 调用WhatsApp Business API
    result, err := h.callWhatsAppAPI(ctx, req)
    if err != nil {
        return nil, fmt.Errorf("failed to send WhatsApp message: %w", err)
    }

    return &MessageResult{
        MessageID:  result.MessageID,
        Status:     "SENT",
        Cost:       h.calculateCost(),
        ExternalID: result.ExternalID,
    }, nil
}

func (h *WhatsAppHandler) ValidateReceiver(receiver string) error {
    // WhatsApp号码格式验证
    phoneRegex := regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
    if !phoneRegex.MatchString(receiver) {
        return fmt.Errorf("invalid WhatsApp number format: %s", receiver)
    }
    return nil
}

func (h *WhatsAppHandler) GetMessageStatus(ctx context.Context, messageID string) (*MessageStatus, error) {
    // 实际实现中会调用WhatsApp API查询状态
    return &MessageStatus{
        Status: "DELIVERED",
        DeliveredAt: &time.Time{},
    }, nil
}

func (h *WhatsAppHandler) calculateCost() float64 {
    // WhatsApp按条收费
    return 0.10 // 每条0.10元
}

func (h *WhatsAppHandler) callWhatsAppAPI(ctx context.Context, req *model.SendMessageRequest) (*WhatsAppResult, error) {
    // 实际的WhatsApp API调用逻辑
    // 这里是示例实现
    return &WhatsAppResult{
        MessageID:  generateMessageID(),
        ExternalID: "wa_" + generateMessageID(),
        Status:     "sent",
    }, nil
}

type WhatsAppResult struct {
    MessageID  string
    ExternalID string
    Status     string
}

// 工具函数
func generateMessageID() string {
    return fmt.Sprintf("%d_%s", time.Now().Unix(), randomString(8))
}

func randomString(length int) string {
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    b := make([]byte, length)
    for i := range b {
        b[i] = charset[rand.Intn(len(charset))]
    }
    return string(b)
}
```
