# 安全架构设计

## 安全设计原则

### 零信任安全模型
- **永不信任，始终验证**: 对所有访问请求进行身份验证和授权
- **最小权限原则**: 用户和服务只获得完成任务所需的最小权限
- **持续监控**: 实时监控所有访问行为和安全事件
- **动态访问控制**: 基于上下文和风险评估的动态权限调整

### 纵深防御策略
```
用户层安全 → 网络层安全 → 应用层安全 → 数据层安全 → 基础设施安全
```

## 身份认证和授权架构

### 统一身份认证系统

```mermaid
graph TB
    subgraph "客户端"
        WEB[Web应用]
        MOBILE[移动应用]
        API[第三方API]
    end
    
    subgraph "认证层"
        GATEWAY[API网关]
        KEYCLOAK[Keycloak身份服务]
        MFA[多因子认证]
    end
    
    subgraph "授权层"
        RBAC[角色权限控制]
        ABAC[属性权限控制]
        OPA[策略引擎]
    end
    
    subgraph "审计层"
        AUDIT[审计日志]
        SIEM[安全信息管理]
    end
    
    WEB --> GATEWAY
    MOBILE --> GATEWAY
    API --> GATEWAY
    
    GATEWAY --> KEYCLOAK
    KEYCLOAK --> MFA
    KEYCLOAK --> RBAC
    RBAC --> ABAC
    ABAC --> OPA
    
    GATEWAY --> AUDIT
    KEYCLOAK --> AUDIT
    AUDIT --> SIEM
```

### OAuth2/JWT实现

```java
@Configuration
@EnableAuthorizationServer
public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {
    
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        clients.inMemory()
            .withClient("cmhk-web")
            .secret(passwordEncoder().encode("web-secret"))
            .authorizedGrantTypes("authorization_code", "refresh_token")
            .scopes("read", "write")
            .accessTokenValiditySeconds(3600)
            .refreshTokenValiditySeconds(86400)
            
            .and()
            .withClient("cmhk-mobile")
            .secret(passwordEncoder().encode("mobile-secret"))
            .authorizedGrantTypes("password", "refresh_token")
            .scopes("read", "write")
            .accessTokenValiditySeconds(7200)
            .refreshTokenValiditySeconds(604800);
    }
    
    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) {
        endpoints
            .tokenStore(jwtTokenStore())
            .accessTokenConverter(jwtAccessTokenConverter())
            .userDetailsService(userDetailsService)
            .authenticationManager(authenticationManager);
    }
}
```

### 多因子认证(MFA)

```java
@Service
public class MFAService {
    
    public MFAChallenge generateChallenge(String userId, MFAType type) {
        switch (type) {
            case SMS:
                return generateSMSChallenge(userId);
            case EMAIL:
                return generateEmailChallenge(userId);
            case TOTP:
                return generateTOTPChallenge(userId);
            case BIOMETRIC:
                return generateBiometricChallenge(userId);
            default:
                throw new UnsupportedMFATypeException(type);
        }
    }
    
    public boolean verifyChallenge(String userId, String challengeId, String response) {
        MFAChallenge challenge = challengeRepository.findById(challengeId);
        
        if (challenge.isExpired()) {
            throw new ChallengeExpiredException();
        }
        
        return challenge.verify(response);
    }
}
```

## 数据安全设计

### 数据分类和保护

```yaml
# 数据分类标准
data_classification:
  public:
    description: "公开数据，无需特殊保护"
    examples: ["产品信息", "公告"]
    protection_level: 0
    
  internal:
    description: "内部数据，需要访问控制"
    examples: ["员工信息", "内部文档"]
    protection_level: 1
    encryption: false
    access_control: rbac
    
  confidential:
    description: "机密数据，需要加密保护"
    examples: ["客户信息", "财务数据"]
    protection_level: 2
    encryption: aes256
    access_control: abac
    audit_required: true
    
  restricted:
    description: "限制数据，最高级别保护"
    examples: ["密码", "支付信息"]
    protection_level: 3
    encryption: aes256_gcm
    access_control: abac
    audit_required: true
    data_masking: true
```

### 数据加密策略

#### 1. 传输加密
```yaml
# TLS配置
tls:
  version: 1.3
  cipher_suites:
    - TLS_AES_256_GCM_SHA384
    - TLS_CHACHA20_POLY1305_SHA256
    - TLS_AES_128_GCM_SHA256
  certificate:
    type: ECC
    curve: P-384
    validity: 1_year
    auto_renewal: true
```

#### 2. 存储加密
```java
@Entity
@Table(name = "customers")
public class Customer {
    
    @Id
    private String id;
    
    @Column(name = "name")
    private String name;
    
    @Encrypted
    @Column(name = "email")
    private String email;
    
    @Encrypted
    @Column(name = "phone")
    private String phone;
    
    @Encrypted
    @Sensitive
    @Column(name = "id_card")
    private String idCard;
}

// 字段级加密实现
@Component
public class FieldEncryption {
    
    @Value("${encryption.key}")
    private String encryptionKey;
    
    public String encrypt(String plaintext) {
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(
                encryptionKey.getBytes(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            
            byte[] encrypted = cipher.doFinal(plaintext.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new EncryptionException("Failed to encrypt data", e);
        }
    }
}
```

#### 3. 密钥管理
```java
@Service
public class KeyManagementService {
    
    @Autowired
    private VaultTemplate vaultTemplate;
    
    public String getEncryptionKey(String keyId) {
        VaultResponse response = vaultTemplate.read("secret/encryption-keys/" + keyId);
        return response.getData().get("key").toString();
    }
    
    public void rotateKey(String keyId) {
        // 生成新密钥
        String newKey = generateNewKey();
        
        // 存储新密钥
        Map<String, Object> keyData = Map.of(
            "key", newKey,
            "created_at", Instant.now(),
            "version", getNextVersion(keyId)
        );
        vaultTemplate.write("secret/encryption-keys/" + keyId, keyData);
        
        // 触发数据重加密
        dataReencryptionService.scheduleReencryption(keyId);
    }
}
```

## 网络安全设计

### 网络分段和隔离

```yaml
# Kubernetes网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cmhk-network-policy
  namespace: cmhk-production
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # 只允许来自API网关的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: cmhk-gateway
    ports:
    - protocol: TCP
      port: 8080
      
  # 允许来自监控系统的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: cmhk-monitoring
    ports:
    - protocol: TCP
      port: 9090
      
  egress:
  # 允许访问数据库
  - to:
    - namespaceSelector:
        matchLabels:
          name: cmhk-database
    ports:
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 6379
      
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
```

### WAF和DDoS防护

```yaml
# Cloudflare WAF规则
waf_rules:
  - name: "SQL注入防护"
    expression: "(http.request.uri.query contains \"union select\") or (http.request.uri.query contains \"drop table\")"
    action: "block"
    
  - name: "XSS防护"
    expression: "(http.request.uri.query contains \"<script\") or (http.request.body contains \"javascript:\")"
    action: "block"
    
  - name: "API限流"
    expression: "(http.request.uri.path matches \"^/api/v1/messages/send$\")"
    action: "rate_limit"
    rate_limit:
      threshold: 100
      period: 60
      
  - name: "地理位置限制"
    expression: "(ip.geoip.country ne \"HK\" and ip.geoip.country ne \"MO\")"
    action: "challenge"
```

## 应用安全设计

### API安全防护

```java
@RestController
@RequestMapping("/api/v1/messages")
@Validated
public class MessageController {
    
    @PostMapping("/send")
    @PreAuthorize("hasPermission('message', 'send')")
    @RateLimited(value = 100, window = 60) // 每分钟100次
    public ResponseEntity<MessageResponse> sendMessage(
            @Valid @RequestBody SendMessageRequest request,
            @RequestHeader("X-Tenant-ID") String tenantId,
            Authentication authentication) {
        
        // 输入验证
        validateRequest(request);
        
        // 租户隔离检查
        validateTenantAccess(tenantId, authentication);
        
        // 业务逻辑处理
        MessageResponse response = messageService.sendMessage(request);
        
        // 审计日志
        auditService.logMessageSent(authentication.getName(), request);
        
        return ResponseEntity.ok(response);
    }
    
    private void validateRequest(SendMessageRequest request) {
        // 防止SQL注入
        if (containsSQLInjection(request.getContent())) {
            throw new SecurityException("Potential SQL injection detected");
        }
        
        // 防止XSS
        if (containsXSS(request.getContent())) {
            throw new SecurityException("Potential XSS attack detected");
        }
        
        // 内容长度限制
        if (request.getContent().length() > 1000) {
            throw new ValidationException("Message content too long");
        }
    }
}
```

### 输入验证和输出编码

```java
@Component
public class SecurityValidator {
    
    private static final Pattern SQL_INJECTION_PATTERN = 
        Pattern.compile("(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute)");
    
    private static final Pattern XSS_PATTERN = 
        Pattern.compile("(?i)(<script|javascript:|onload=|onerror=)");
    
    public boolean containsSQLInjection(String input) {
        return SQL_INJECTION_PATTERN.matcher(input).find();
    }
    
    public boolean containsXSS(String input) {
        return XSS_PATTERN.matcher(input).find();
    }
    
    public String sanitizeInput(String input) {
        return input
            .replaceAll("<", "&lt;")
            .replaceAll(">", "&gt;")
            .replaceAll("\"", "&quot;")
            .replaceAll("'", "&#x27;")
            .replaceAll("/", "&#x2F;");
    }
}
```

## 安全监控和审计

### 安全事件监控

```java
@Component
public class SecurityEventMonitor {
    
    @EventListener
    public void handleLoginFailure(AuthenticationFailureEvent event) {
        SecurityEvent securityEvent = SecurityEvent.builder()
            .type(SecurityEventType.LOGIN_FAILURE)
            .username(event.getAuthentication().getName())
            .ipAddress(getClientIP())
            .timestamp(Instant.now())
            .details(Map.of(
                "reason", event.getException().getMessage(),
                "user_agent", getUserAgent()
            ))
            .build();
            
        securityEventService.recordEvent(securityEvent);
        
        // 检查是否需要锁定账户
        if (shouldLockAccount(event.getAuthentication().getName())) {
            userService.lockAccount(event.getAuthentication().getName());
            alertService.sendSecurityAlert("Account locked due to multiple failed login attempts");
        }
    }
    
    @EventListener
    public void handleSuspiciousActivity(SuspiciousActivityEvent event) {
        // 记录可疑活动
        securityEventService.recordEvent(event.toSecurityEvent());
        
        // 实时风险评估
        RiskScore riskScore = riskAssessmentService.calculateRisk(event);
        
        if (riskScore.isHigh()) {
            // 触发安全响应
            securityResponseService.handleHighRiskActivity(event);
        }
    }
}
```

### 审计日志设计

```java
@Entity
@Table(name = "audit_logs")
public class AuditLog {
    
    @Id
    private String id;
    
    @Column(name = "user_id")
    private String userId;
    
    @Column(name = "action")
    private String action;
    
    @Column(name = "resource")
    private String resource;
    
    @Column(name = "ip_address")
    private String ipAddress;
    
    @Column(name = "user_agent")
    private String userAgent;
    
    @Column(name = "request_data", columnDefinition = "JSON")
    private String requestData;
    
    @Column(name = "response_status")
    private Integer responseStatus;
    
    @Column(name = "timestamp")
    private Instant timestamp;
    
    @Column(name = "tenant_id")
    private String tenantId;
}

@Aspect
@Component
public class AuditAspect {
    
    @Around("@annotation(Auditable)")
    public Object auditMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        AuditLog auditLog = AuditLog.builder()
            .action(methodName)
            .requestData(JsonUtils.toJson(args))
            .timestamp(Instant.now())
            .userId(getCurrentUserId())
            .ipAddress(getCurrentIP())
            .tenantId(getCurrentTenantId())
            .build();
        
        try {
            Object result = joinPoint.proceed();
            auditLog.setResponseStatus(200);
            return result;
        } catch (Exception e) {
            auditLog.setResponseStatus(500);
            auditLog.setErrorMessage(e.getMessage());
            throw e;
        } finally {
            auditLogService.save(auditLog);
        }
    }
}
```

## 合规性设计

### GDPR合规

```java
@Service
public class DataPrivacyService {
    
    public void handleDataSubjectRequest(DataSubjectRequest request) {
        switch (request.getType()) {
            case ACCESS:
                handleAccessRequest(request);
                break;
            case RECTIFICATION:
                handleRectificationRequest(request);
                break;
            case ERASURE:
                handleErasureRequest(request);
                break;
            case PORTABILITY:
                handlePortabilityRequest(request);
                break;
        }
    }
    
    private void handleErasureRequest(DataSubjectRequest request) {
        String customerId = request.getCustomerId();
        
        // 检查数据保留要求
        if (hasLegalRetentionRequirement(customerId)) {
            throw new DataRetentionException("Data cannot be erased due to legal requirements");
        }
        
        // 匿名化处理
        customerService.anonymizeCustomer(customerId);
        
        // 删除相关数据
        messageService.deleteCustomerMessages(customerId);
        analyticsService.deleteCustomerAnalytics(customerId);
        
        // 记录删除操作
        auditService.logDataErasure(customerId, request.getRequestId());
    }
}
```

### 数据本地化

```yaml
# 数据本地化配置
data_localization:
  hong_kong:
    customer_data: required
    message_content: required
    analytics_data: allowed_offshore
    backup_data: required
    
  mainland_china:
    customer_data: prohibited
    message_content: prohibited
    analytics_data: prohibited
    backup_data: prohibited
    
  other_regions:
    customer_data: prohibited
    message_content: allowed_transit
    analytics_data: allowed_aggregated
    backup_data: allowed_encrypted
```

## 安全运营

### 安全事件响应流程

```mermaid
graph TD
    A[安全事件检测] --> B{事件分类}
    B -->|低风险| C[自动处理]
    B -->|中风险| D[人工审查]
    B -->|高风险| E[紧急响应]
    
    C --> F[记录日志]
    D --> G[安全分析师处理]
    E --> H[安全团队响应]
    
    G --> I{需要升级?}
    I -->|是| E
    I -->|否| F
    
    H --> J[隔离威胁]
    J --> K[影响评估]
    K --> L[恢复操作]
    L --> M[事后分析]
    M --> F
```

### 安全培训和意识

```yaml
# 安全培训计划
security_training:
  developers:
    frequency: quarterly
    topics:
      - secure_coding_practices
      - owasp_top_10
      - api_security
      - data_protection
      
  operations:
    frequency: monthly
    topics:
      - incident_response
      - security_monitoring
      - compliance_requirements
      - threat_intelligence
      
  all_staff:
    frequency: annually
    topics:
      - phishing_awareness
      - password_security
      - social_engineering
      - data_handling
```

通过以上安全架构设计，可以建立完善的安全防护体系，确保CMHK通信能力开放平台的安全性和合规性。

---

*本安全架构设计基于零信任安全模型和纵深防御策略，符合香港地区的法规要求和国际安全标准。*
