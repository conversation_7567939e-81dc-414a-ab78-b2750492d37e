package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"cmhk-platform/internal/api"
	"cmhk-platform/internal/communication/handler"
	"cmhk-platform/internal/communication/repository"
	"cmhk-platform/internal/communication/service"
	"cmhk-platform/internal/shared/auth"
	"cmhk-platform/internal/shared/cache"
	"cmhk-platform/internal/shared/config"
	"cmhk-platform/internal/shared/database"
	userHandler "cmhk-platform/internal/user/handler"
	userRepo "cmhk-platform/internal/user/repository"
	userService "cmhk-platform/internal/user/service"
	"cmhk-platform/pkg/logger"
)

func main() {
	// Initialize logger
	log := logger.NewLogger()
	defer log.Sync()

	log.Info("Starting CMHK Communication Platform...")

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load configuration", zap.Error(err))
	}

	log.Info("Configuration loaded successfully")

	// Initialize database connections
	deps, err := initializeDependencies(cfg, log)
	if err != nil {
		log.Fatal("Failed to initialize dependencies", zap.Error(err))
	}

	log.Info("Dependencies initialized successfully")

	// Set Gin mode
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize router
	router := api.NewRouter(deps)

	// Create HTTP server
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Info("Starting HTTP server",
			zap.Int("port", cfg.Server.Port),
			zap.String("mode", cfg.Server.Mode))

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start HTTP server", zap.Error(err))
		}
	}()

	log.Info("CMHK Communication Platform started successfully")

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("Shutting down server...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown", zap.Error(err))
	}

	// Close database connections
	closeDependencies(deps, log)

	log.Info("Server shutdown completed")
}

// initializeDependencies initializes all application dependencies
func initializeDependencies(cfg *config.Config, log *logger.Logger) (*api.Dependencies, error) {
	// Initialize MySQL connection
	log.Info("Connecting to MySQL database...")
	mysqlDB, err := database.NewMySQLConnection(cfg.Database.MySQL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MySQL: %w", err)
	}
	log.Info("MySQL connection established")

	// Initialize Redis connection
	log.Info("Connecting to Redis...")
	redisClient, err := database.NewRedisSingleConnection(cfg.Database.Redis)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}
	log.Info("Redis connection established")

	// Initialize ClickHouse connection (optional)
	var clickhouseDB *gorm.DB
	if cfg.Database.ClickHouse.Host != "" {
		log.Info("Connecting to ClickHouse...")
		clickhouseDB, err = database.NewClickHouseConnection(cfg.Database.ClickHouse)
		if err != nil {
			log.Warn("Failed to connect to ClickHouse, continuing without it", zap.Error(err))
		} else {
			log.Info("ClickHouse connection established")
		}
	}

	// Initialize JWT manager
	jwtManager := auth.NewJWTManager(
		cfg.Auth.JWTSecret,
		time.Duration(cfg.Auth.TokenExpiry)*time.Second,
		time.Duration(cfg.Auth.RefreshExpiry)*time.Second,
	)

	// Initialize cache manager
	redisCache := cache.NewRedisCache(redisClient)
	cacheManager := cache.NewCacheManager(redisCache, time.Duration(cfg.Cache.DefaultTTL)*time.Second)

	// Initialize repositories
	userRepository := userRepo.NewUserRepository(mysqlDB)
	roleRepository := userRepo.NewRoleRepository(mysqlDB)
	messageRepository := repository.NewMessageRepository(mysqlDB)
	templateRepository := repository.NewTemplateRepository(mysqlDB)

	// Initialize services
	channelService := service.NewChannelService(cfg, log)
	userSvc := userService.NewUserService(userRepository, roleRepository, jwtManager, cacheManager, log)
	messageSvc := service.NewMessageService(messageRepository, templateRepository, channelService, log)

	// Initialize handlers
	userHdl := userHandler.NewUserHandler(userSvc, log)
	messageHdl := handler.NewMessageHandler(messageSvc, log)

	return &api.Dependencies{
		Logger:     log,
		Config:     cfg,
		MySQL:      mysqlDB,
		Redis:      redisClient,
		ClickHouse: clickhouseDB,
		JWTManager: jwtManager,

		// Services
		UserService:    userSvc,
		MessageService: messageSvc,

		// Handlers
		UserHandler:    userHdl,
		MessageHandler: messageHdl,
	}, nil
}

// closeDependencies closes all database connections
func closeDependencies(deps *api.Dependencies, log *logger.Logger) {
	// Close MySQL connection
	if deps.MySQL != nil {
		if sqlDB, err := deps.MySQL.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				log.Error("Failed to close MySQL connection", zap.Error(err))
			} else {
				log.Info("MySQL connection closed")
			}
		}
	}

	// Close Redis connection
	if deps.Redis != nil {
		if err := deps.Redis.Close(); err != nil {
			log.Error("Failed to close Redis connection", zap.Error(err))
		} else {
			log.Info("Redis connection closed")
		}
	}

	// Close ClickHouse connection
	if deps.ClickHouse != nil {
		if sqlDB, err := deps.ClickHouse.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				log.Error("Failed to close ClickHouse connection", zap.Error(err))
			} else {
				log.Info("ClickHouse connection closed")
			}
		}
	}
}
