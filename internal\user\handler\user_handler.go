package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"cmhk-platform/internal/shared/auth"
	"cmhk-platform/internal/shared/types"
	"cmhk-platform/internal/user/model"
	"cmhk-platform/internal/user/service"
	"cmhk-platform/pkg/logger"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	userService service.UserService
	logger      *logger.Logger
}

// NewUserHandler creates a new user handler
func NewUserHandler(userService service.UserService, logger *logger.Logger) *UserHandler {
	return &UserHandler{
		userService: userService,
		logger:      logger,
	}
}

// CreateUser handles user creation requests
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req model.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	user, err := h.userService.CreateUser(c.Request.Context(), req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "User created successfully", user.ToUserInfo())
}

// GetUser handles get user by ID requests
func (h *UserHandler) GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid user ID")
		return
	}

	user, err := h.userService.GetUser(c.Request.Context(), id)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponse(c, user.ToUserInfo())
}

// UpdateUser handles user update requests
func (h *UserHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid user ID")
		return
	}

	var req model.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	user, err := h.userService.UpdateUser(c.Request.Context(), id, req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "User updated successfully", user.ToUserInfo())
}

// DeleteUser handles user deletion requests
func (h *UserHandler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid user ID")
		return
	}

	if err := h.userService.DeleteUser(c.Request.Context(), id); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "User deleted successfully", nil)
}

// ListUsers handles user listing requests
func (h *UserHandler) ListUsers(c *gin.Context) {
	var query model.UserListQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	// Get current tenant ID from context
	if tenantID, ok := auth.GetCurrentTenantID(c); ok {
		query.TenantID = tenantID
	}

	users, total, err := h.userService.ListUsers(c.Request.Context(), query)
	if err != nil {
		h.handleError(c, err)
		return
	}

	// Convert to UserInfo DTOs
	userInfos := make([]model.UserInfo, len(users))
	for i, user := range users {
		userInfos[i] = user.ToUserInfo()
	}

	pagination := types.NewPagination(query.Page, query.PageSize, total)
	types.PaginatedSuccessResponse(c, userInfos, pagination)
}

// ChangePassword handles password change requests
func (h *UserHandler) ChangePassword(c *gin.Context) {
	userID, ok := auth.GetCurrentUserID(c)
	if !ok {
		types.ErrorResponse(c, http.StatusUnauthorized, types.ErrorCodeUnauthorized, "User not authenticated")
		return
	}

	var req model.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	if err := h.userService.ChangePassword(c.Request.Context(), userID, req); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Password changed successfully", nil)
}

// Login handles user login requests
func (h *UserHandler) Login(c *gin.Context) {
	var req model.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	response, err := h.userService.Login(c.Request.Context(), req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Login successful", response)
}

// RefreshToken handles token refresh requests
func (h *UserHandler) RefreshToken(c *gin.Context) {
	var req model.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	response, err := h.userService.RefreshToken(c.Request.Context(), req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Token refreshed successfully", response)
}

// Logout handles user logout requests
func (h *UserHandler) Logout(c *gin.Context) {
	userID, ok := auth.GetCurrentUserID(c)
	if !ok {
		types.ErrorResponse(c, http.StatusUnauthorized, types.ErrorCodeUnauthorized, "User not authenticated")
		return
	}

	// Get refresh token from request body (optional)
	var req struct {
		RefreshToken string `json:"refresh_token"`
	}
	c.ShouldBindJSON(&req)

	if err := h.userService.Logout(c.Request.Context(), userID, req.RefreshToken); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Logout successful", nil)
}

// AssignRole handles role assignment requests
func (h *UserHandler) AssignRole(c *gin.Context) {
	var req model.AssignRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	grantedBy, ok := auth.GetCurrentUserID(c)
	if !ok {
		types.ErrorResponse(c, http.StatusUnauthorized, types.ErrorCodeUnauthorized, "User not authenticated")
		return
	}

	if err := h.userService.AssignRole(c.Request.Context(), req.UserID, req.RoleID, grantedBy); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Role assigned successfully", nil)
}

// RemoveRole handles role removal requests
func (h *UserHandler) RemoveRole(c *gin.Context) {
	var req struct {
		UserID uint64 `json:"user_id" binding:"required"`
		RoleID uint64 `json:"role_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		types.ValidationErrorResponse(c, map[string]string{"error": err.Error()})
		return
	}

	if err := h.userService.RemoveRole(c.Request.Context(), req.UserID, req.RoleID); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "Role removed successfully", nil)
}

// LockUser handles user locking requests
func (h *UserHandler) LockUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid user ID")
		return
	}

	if err := h.userService.LockUser(c.Request.Context(), id); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "User locked successfully", nil)
}

// UnlockUser handles user unlocking requests
func (h *UserHandler) UnlockUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		types.ErrorResponse(c, http.StatusBadRequest, types.ErrorCodeValidation, "Invalid user ID")
		return
	}

	if err := h.userService.UnlockUser(c.Request.Context(), id); err != nil {
		h.handleError(c, err)
		return
	}

	types.SuccessResponseWithMessage(c, "User unlocked successfully", nil)
}

// handleError handles service errors and converts them to appropriate HTTP responses
func (h *UserHandler) handleError(c *gin.Context, err error) {
	h.logger.Error("User handler error", zap.Error(err))

	switch err {
	case model.ErrUserNotFound:
		types.ErrorResponse(c, http.StatusNotFound, model.GetErrorCode(err), err.Error())
	case model.ErrUserAlreadyExists:
		types.ErrorResponse(c, http.StatusConflict, model.GetErrorCode(err), err.Error())
	case model.ErrUserInactive:
		types.ErrorResponse(c, http.StatusForbidden, model.GetErrorCode(err), err.Error())
	case model.ErrUserLocked:
		types.ErrorResponse(c, http.StatusForbidden, model.GetErrorCode(err), err.Error())
	case model.ErrUserSuspended:
		types.ErrorResponse(c, http.StatusForbidden, model.GetErrorCode(err), err.Error())
	case model.ErrInvalidCredentials:
		types.ErrorResponse(c, http.StatusUnauthorized, model.GetErrorCode(err), err.Error())
	case model.ErrPasswordTooWeak:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrSamePassword:
		types.ErrorResponse(c, http.StatusBadRequest, model.GetErrorCode(err), err.Error())
	case model.ErrTooManyFailedLogins:
		types.ErrorResponse(c, http.StatusTooManyRequests, model.GetErrorCode(err), err.Error())
	case model.ErrInvalidToken:
		types.ErrorResponse(c, http.StatusUnauthorized, model.GetErrorCode(err), err.Error())
	case model.ErrTokenExpired:
		types.ErrorResponse(c, http.StatusUnauthorized, model.GetErrorCode(err), err.Error())
	case model.ErrRefreshTokenInvalid:
		types.ErrorResponse(c, http.StatusUnauthorized, model.GetErrorCode(err), err.Error())
	case model.ErrRoleNotFound:
		types.ErrorResponse(c, http.StatusNotFound, model.GetErrorCode(err), err.Error())
	case model.ErrRoleAlreadyExists:
		types.ErrorResponse(c, http.StatusConflict, model.GetErrorCode(err), err.Error())
	case model.ErrInsufficientPermissions:
		types.ErrorResponse(c, http.StatusForbidden, model.GetErrorCode(err), err.Error())
	case model.ErrPermissionDenied:
		types.ErrorResponse(c, http.StatusForbidden, model.GetErrorCode(err), err.Error())
	default:
		types.ErrorResponse(c, http.StatusInternalServerError, types.ErrorCodeInternalError, "Internal server error")
	}
}
