package model

import (
	"time"
	"gorm.io/gorm"
)

// File represents a file in the system
type File struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_type" json:"tenant_id"`
	FileCode    string         `gorm:"size:100;not null;uniqueIndex" json:"file_code"`
	FileName    string         `gorm:"size:255;not null" json:"file_name"`
	OriginalName string        `gorm:"size:255;not null" json:"original_name"`
	FileType    string         `gorm:"size:20;not null;index:idx_tenant_type" json:"file_type"`
	MimeType    string         `gorm:"size:100;not null" json:"mime_type"`
	FileSize    int64          `gorm:"not null" json:"file_size"`
	FilePath    string         `gorm:"size:500;not null" json:"file_path"`
	StorageType string         `gorm:"size:20;not null" json:"storage_type"`
	Bucket      string         `gorm:"size:100" json:"bucket"`
	URL         string         `gorm:"size:500" json:"url"`
	ThumbnailURL string        `gorm:"size:500" json:"thumbnail_url"`
	Hash        string         `gorm:"size:64;index" json:"hash"`
	Status      int8           `gorm:"default:1" json:"status"`
	IsPublic    bool           `gorm:"default:false" json:"is_public"`
	ExpiresAt   *time.Time     `json:"expires_at"`
	UploadedBy  uint64         `gorm:"not null" json:"uploaded_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Tags []FileTag `gorm:"many2many:file_tag_relations;" json:"tags,omitempty"`
}

// FileTag represents a file tag
type FileTag struct {
	ID        uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint64         `gorm:"not null;uniqueIndex:uk_tenant_tag" json:"tenant_id"`
	TagName   string         `gorm:"size:50;not null;uniqueIndex:uk_tenant_tag" json:"tag_name"`
	TagColor  string         `gorm:"size:7;default:#007bff" json:"tag_color"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// FileTagRelation represents the many-to-many relationship between files and tags
type FileTagRelation struct {
	ID     uint64 `gorm:"primaryKey;autoIncrement" json:"id"`
	FileID uint64 `gorm:"not null;uniqueIndex:uk_file_tag" json:"file_id"`
	TagID  uint64 `gorm:"not null;uniqueIndex:uk_file_tag" json:"tag_id"`
}

// FileShare represents file sharing settings
type FileShare struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_file" json:"tenant_id"`
	FileID      uint64         `gorm:"not null;index:idx_tenant_file" json:"file_id"`
	ShareCode   string         `gorm:"size:32;not null;uniqueIndex" json:"share_code"`
	ShareType   string         `gorm:"size:20;not null" json:"share_type"`
	Password    string         `gorm:"size:255" json:"password"`
	MaxDownloads int           `gorm:"default:0" json:"max_downloads"`
	DownloadCount int          `gorm:"default:0" json:"download_count"`
	ExpiresAt   *time.Time     `json:"expires_at"`
	IsActive    bool           `gorm:"default:true" json:"is_active"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// FileAccess represents file access log
type FileAccess struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint64    `gorm:"not null;index:idx_tenant_file" json:"tenant_id"`
	FileID    uint64    `gorm:"not null;index:idx_tenant_file" json:"file_id"`
	UserID    uint64    `json:"user_id"`
	Action    string    `gorm:"size:20;not null" json:"action"`
	IPAddress string    `gorm:"size:45" json:"ip_address"`
	UserAgent string    `gorm:"size:500" json:"user_agent"`
	CreatedAt time.Time `gorm:"index:idx_created_at" json:"created_at"`
}

// File types
const (
	FileTypeImage    = "image"
	FileTypeVideo    = "video"
	FileTypeAudio    = "audio"
	FileTypeDocument = "document"
	FileTypeArchive  = "archive"
	FileTypeOther    = "other"
)

// Storage types
const (
	StorageTypeLocal = "local"
	StorageTypeS3    = "s3"
	StorageTypeOSS   = "oss"
	StorageTypeCOS   = "cos"
)

// File statuses
const (
	FileStatusUploading = 0
	FileStatusActive    = 1
	FileStatusArchived  = 2
	FileStatusDeleted   = 3
)

// Share types
const (
	ShareTypePublic   = "public"
	ShareTypePrivate  = "private"
	ShareTypePassword = "password"
)

// Access actions
const (
	ActionView     = "view"
	ActionDownload = "download"
	ActionShare    = "share"
	ActionDelete   = "delete"
)

// TableName returns the table name for File
func (File) TableName() string {
	return "files"
}

// TableName returns the table name for FileTag
func (FileTag) TableName() string {
	return "file_tags"
}

// TableName returns the table name for FileTagRelation
func (FileTagRelation) TableName() string {
	return "file_tag_relations"
}

// TableName returns the table name for FileShare
func (FileShare) TableName() string {
	return "file_shares"
}

// TableName returns the table name for FileAccess
func (FileAccess) TableName() string {
	return "file_access_logs"
}

// IsActive checks if file is active
func (f *File) IsActive() bool {
	return f.Status == FileStatusActive
}

// IsExpired checks if file is expired
func (f *File) IsExpired() bool {
	return f.ExpiresAt != nil && f.ExpiresAt.Before(time.Now())
}

// GetSizeInMB returns file size in MB
func (f *File) GetSizeInMB() float64 {
	return float64(f.FileSize) / (1024 * 1024)
}

// IsImage checks if file is an image
func (f *File) IsImage() bool {
	return f.FileType == FileTypeImage
}

// IsActive checks if share is active
func (s *FileShare) IsActive() bool {
	return s.IsActive && !s.IsExpired()
}

// IsExpired checks if share is expired
func (s *FileShare) IsExpired() bool {
	return s.ExpiresAt != nil && s.ExpiresAt.Before(time.Now())
}

// CanDownload checks if share can be downloaded
func (s *FileShare) CanDownload() bool {
	if !s.IsActive() {
		return false
	}
	
	if s.MaxDownloads > 0 && s.DownloadCount >= s.MaxDownloads {
		return false
	}
	
	return true
}

// ValidateFileType validates if file type is supported
func ValidateFileType(fileType string) bool {
	supportedTypes := []string{
		FileTypeImage,
		FileTypeVideo,
		FileTypeAudio,
		FileTypeDocument,
		FileTypeArchive,
		FileTypeOther,
	}
	
	for _, supported := range supportedTypes {
		if fileType == supported {
			return true
		}
	}
	
	return false
}

// ValidateStorageType validates if storage type is supported
func ValidateStorageType(storageType string) bool {
	supportedTypes := []string{
		StorageTypeLocal,
		StorageTypeS3,
		StorageTypeOSS,
		StorageTypeCOS,
	}
	
	for _, supported := range supportedTypes {
		if storageType == supported {
			return true
		}
	}
	
	return false
}

// GetFileTypeFromMime returns file type based on MIME type
func GetFileTypeFromMime(mimeType string) string {
	switch {
	case mimeType[:5] == "image":
		return FileTypeImage
	case mimeType[:5] == "video":
		return FileTypeVideo
	case mimeType[:5] == "audio":
		return FileTypeAudio
	case mimeType == "application/pdf" || 
		 mimeType == "application/msword" ||
		 mimeType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
		return FileTypeDocument
	case mimeType == "application/zip" ||
		 mimeType == "application/x-rar-compressed" ||
		 mimeType == "application/x-tar":
		return FileTypeArchive
	default:
		return FileTypeOther
	}
}
