package repository

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"cmhk-platform/internal/customer/model"
)

// CustomerActivityRepository defines the interface for customer activity data access
type CustomerActivityRepository interface {
	Create(ctx context.Context, activity *model.CustomerActivity) error
	GetByID(ctx context.Context, id uint64) (*model.CustomerActivity, error)
	List(ctx context.Context, customerID uint64, page, pageSize int) ([]*model.CustomerActivity, int64, error)
	ListByType(ctx context.Context, customerID uint64, activityType string, page, pageSize int) ([]*model.CustomerActivity, int64, error)
	GetRecentActivities(ctx context.Context, tenantID uint64, limit int) ([]*model.CustomerActivity, error)
	GetActivityCount(ctx context.Context, customerID uint64) (int64, error)
	GetActivityCountByType(ctx context.Context, customerID uint64, activityType string) (int64, error)
	DeleteOldActivities(ctx context.Context, tenantID uint64, beforeDate time.Time) error
}

// customerActivityRepository implements CustomerActivityRepository interface
type customerActivityRepository struct {
	db *gorm.DB
}

// NewCustomerActivityRepository creates a new customer activity repository
func NewCustomerActivityRepository(db *gorm.DB) CustomerActivityRepository {
	return &customerActivityRepository{db: db}
}

// Create creates a new customer activity
func (r *customerActivityRepository) Create(ctx context.Context, activity *model.CustomerActivity) error {
	if err := r.db.WithContext(ctx).Create(activity).Error; err != nil {
		return fmt.Errorf("failed to create customer activity: %w", err)
	}
	return nil
}

// GetByID retrieves a customer activity by ID
func (r *customerActivityRepository) GetByID(ctx context.Context, id uint64) (*model.CustomerActivity, error) {
	var activity model.CustomerActivity
	err := r.db.WithContext(ctx).First(&activity, id).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrActivityNotFound
		}
		return nil, fmt.Errorf("failed to get customer activity by ID: %w", err)
	}
	
	return &activity, nil
}

// List retrieves customer activities with pagination
func (r *customerActivityRepository) List(ctx context.Context, customerID uint64, page, pageSize int) ([]*model.CustomerActivity, int64, error) {
	var activities []*model.CustomerActivity
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.CustomerActivity{}).
		Where("customer_id = ?", customerID)
	
	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customer activities: %w", err)
	}
	
	// Apply pagination
	offset := (page - 1) * pageSize
	if err := db.Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&activities).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list customer activities: %w", err)
	}
	
	return activities, total, nil
}

// ListByType retrieves customer activities by type with pagination
func (r *customerActivityRepository) ListByType(ctx context.Context, customerID uint64, activityType string, page, pageSize int) ([]*model.CustomerActivity, int64, error) {
	var activities []*model.CustomerActivity
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.CustomerActivity{}).
		Where("customer_id = ? AND activity_type = ?", customerID, activityType)
	
	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customer activities by type: %w", err)
	}
	
	// Apply pagination
	offset := (page - 1) * pageSize
	if err := db.Offset(offset).
		Limit(pageSize).
		Order("created_at DESC").
		Find(&activities).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list customer activities by type: %w", err)
	}
	
	return activities, total, nil
}

// GetRecentActivities retrieves recent activities for a tenant
func (r *customerActivityRepository) GetRecentActivities(ctx context.Context, tenantID uint64, limit int) ([]*model.CustomerActivity, error) {
	var activities []*model.CustomerActivity
	
	err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("created_at DESC").
		Limit(limit).
		Find(&activities).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get recent activities: %w", err)
	}
	
	return activities, nil
}

// GetActivityCount returns the total number of activities for a customer
func (r *customerActivityRepository) GetActivityCount(ctx context.Context, customerID uint64) (int64, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&model.CustomerActivity{}).
		Where("customer_id = ?", customerID).
		Count(&count).Error
	
	if err != nil {
		return 0, fmt.Errorf("failed to get activity count: %w", err)
	}
	
	return count, nil
}

// GetActivityCountByType returns the number of activities by type for a customer
func (r *customerActivityRepository) GetActivityCountByType(ctx context.Context, customerID uint64, activityType string) (int64, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&model.CustomerActivity{}).
		Where("customer_id = ? AND activity_type = ?", customerID, activityType).
		Count(&count).Error
	
	if err != nil {
		return 0, fmt.Errorf("failed to get activity count by type: %w", err)
	}
	
	return count, nil
}

// DeleteOldActivities deletes activities older than the specified date
func (r *customerActivityRepository) DeleteOldActivities(ctx context.Context, tenantID uint64, beforeDate time.Time) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND created_at < ?", tenantID, beforeDate).
		Delete(&model.CustomerActivity{})
	
	if result.Error != nil {
		return fmt.Errorf("failed to delete old activities: %w", result.Error)
	}
	
	return nil
}
