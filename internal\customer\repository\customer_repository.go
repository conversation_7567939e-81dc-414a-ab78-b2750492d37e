package repository

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"cmhk-platform/internal/customer/model"
)

// CustomerRepository defines the interface for customer data access
type CustomerRepository interface {
	Create(ctx context.Context, customer *model.Customer) error
	GetByID(ctx context.Context, id uint64) (*model.Customer, error)
	GetByCode(ctx context.Context, tenantID uint64, code string) (*model.Customer, error)
	GetByEmail(ctx context.Context, email string) (*model.Customer, error)
	GetByPhone(ctx context.Context, phone string) (*model.Customer, error)
	Update(ctx context.Context, customer *model.Customer) error
	Delete(ctx context.Context, id uint64) error
	List(ctx context.Context, query model.CustomerListQuery) ([]*model.Customer, int64, error)
	GetCustomerGroups(ctx context.Context, customerID uint64) ([]*model.CustomerGroup, error)
	AddToGroup(ctx context.Context, customerID, groupID uint64) error
	RemoveFromGroup(ctx context.Context, customerID, groupID uint64) error
	GetCustomerStats(ctx context.Context, tenantID uint64) (*model.CustomerStatsResponse, error)
	HasActivities(ctx context.Context, customerID uint64) (bool, error)
}

// customerRepository implements CustomerRepository interface
type customerRepository struct {
	db *gorm.DB
}

// NewCustomerRepository creates a new customer repository
func NewCustomerRepository(db *gorm.DB) CustomerRepository {
	return &customerRepository{db: db}
}

// Create creates a new customer
func (r *customerRepository) Create(ctx context.Context, customer *model.Customer) error {
	if err := r.db.WithContext(ctx).Create(customer).Error; err != nil {
		return fmt.Errorf("failed to create customer: %w", err)
	}
	return nil
}

// GetByID retrieves a customer by ID
func (r *customerRepository) GetByID(ctx context.Context, id uint64) (*model.Customer, error) {
	var customer model.Customer
	err := r.db.WithContext(ctx).
		Preload("Groups").
		First(&customer, id).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrCustomerNotFound
		}
		return nil, fmt.Errorf("failed to get customer by ID: %w", err)
	}
	
	return &customer, nil
}

// GetByCode retrieves a customer by tenant ID and customer code
func (r *customerRepository) GetByCode(ctx context.Context, tenantID uint64, code string) (*model.Customer, error) {
	var customer model.Customer
	err := r.db.WithContext(ctx).
		Preload("Groups").
		Where("tenant_id = ? AND customer_code = ?", tenantID, code).
		First(&customer).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrCustomerNotFound
		}
		return nil, fmt.Errorf("failed to get customer by code: %w", err)
	}
	
	return &customer, nil
}

// GetByEmail retrieves a customer by email
func (r *customerRepository) GetByEmail(ctx context.Context, email string) (*model.Customer, error) {
	var customer model.Customer
	err := r.db.WithContext(ctx).
		Preload("Groups").
		Where("email = ?", email).
		First(&customer).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrCustomerNotFound
		}
		return nil, fmt.Errorf("failed to get customer by email: %w", err)
	}
	
	return &customer, nil
}

// GetByPhone retrieves a customer by phone
func (r *customerRepository) GetByPhone(ctx context.Context, phone string) (*model.Customer, error) {
	var customer model.Customer
	err := r.db.WithContext(ctx).
		Preload("Groups").
		Where("phone = ?", phone).
		First(&customer).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrCustomerNotFound
		}
		return nil, fmt.Errorf("failed to get customer by phone: %w", err)
	}
	
	return &customer, nil
}

// Update updates a customer
func (r *customerRepository) Update(ctx context.Context, customer *model.Customer) error {
	if err := r.db.WithContext(ctx).Save(customer).Error; err != nil {
		return fmt.Errorf("failed to update customer: %w", err)
	}
	return nil
}

// Delete soft deletes a customer
func (r *customerRepository) Delete(ctx context.Context, id uint64) error {
	if err := r.db.WithContext(ctx).Delete(&model.Customer{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete customer: %w", err)
	}
	return nil
}

// List retrieves customers with pagination and filtering
func (r *customerRepository) List(ctx context.Context, query model.CustomerListQuery) ([]*model.Customer, int64, error) {
	var customers []*model.Customer
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.Customer{})
	
	// Apply filters
	if query.TenantID > 0 {
		db = db.Where("tenant_id = ?", query.TenantID)
	}
	
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}
	
	if query.Gender != "" {
		db = db.Where("gender = ?", query.Gender)
	}
	
	if query.Search != "" {
		searchPattern := "%" + query.Search + "%"
		db = db.Where("first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR phone LIKE ? OR customer_code LIKE ?", 
			searchPattern, searchPattern, searchPattern, searchPattern, searchPattern)
	}
	
	if !query.DateFrom.IsZero() {
		db = db.Where("created_at >= ?", query.DateFrom)
	}
	
	if !query.DateTo.IsZero() {
		db = db.Where("created_at <= ?", query.DateTo)
	}
	
	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customers: %w", err)
	}
	
	// Apply pagination
	offset := (query.Page - 1) * query.PageSize
	if err := db.Preload("Groups").
		Offset(offset).
		Limit(query.PageSize).
		Order("created_at DESC").
		Find(&customers).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list customers: %w", err)
	}
	
	return customers, total, nil
}

// GetCustomerGroups retrieves all groups for a customer
func (r *customerRepository) GetCustomerGroups(ctx context.Context, customerID uint64) ([]*model.CustomerGroup, error) {
	var groups []*model.CustomerGroup
	
	err := r.db.WithContext(ctx).
		Table("customer_groups").
		Joins("JOIN customer_group_members ON customer_groups.id = customer_group_members.group_id").
		Where("customer_group_members.customer_id = ? AND customer_groups.status = ?", customerID, model.GroupStatusActive).
		Find(&groups).Error
	
	if err != nil {
		return nil, fmt.Errorf("failed to get customer groups: %w", err)
	}
	
	return groups, nil
}

// AddToGroup adds a customer to a group
func (r *customerRepository) AddToGroup(ctx context.Context, customerID, groupID uint64) error {
	member := &model.CustomerGroupMember{
		CustomerID: customerID,
		GroupID:    groupID,
	}
	
	if err := r.db.WithContext(ctx).Create(member).Error; err != nil {
		return fmt.Errorf("failed to add customer to group: %w", err)
	}
	
	return nil
}

// RemoveFromGroup removes a customer from a group
func (r *customerRepository) RemoveFromGroup(ctx context.Context, customerID, groupID uint64) error {
	if err := r.db.WithContext(ctx).
		Where("customer_id = ? AND group_id = ?", customerID, groupID).
		Delete(&model.CustomerGroupMember{}).Error; err != nil {
		return fmt.Errorf("failed to remove customer from group: %w", err)
	}
	
	return nil
}

// GetCustomerStats retrieves customer statistics
func (r *customerRepository) GetCustomerStats(ctx context.Context, tenantID uint64) (*model.CustomerStatsResponse, error) {
	stats := &model.CustomerStatsResponse{
		GenderStats: make(map[string]int64),
		StatusStats: make(map[string]int64),
	}
	
	// Get total customers
	if err := r.db.WithContext(ctx).
		Model(&model.Customer{}).
		Where("tenant_id = ?", tenantID).
		Count(&stats.TotalCustomers).Error; err != nil {
		return nil, fmt.Errorf("failed to get total customers: %w", err)
	}
	
	// Get active customers
	if err := r.db.WithContext(ctx).
		Model(&model.Customer{}).
		Where("tenant_id = ? AND status = ?", tenantID, model.CustomerStatusActive).
		Count(&stats.ActiveCustomers).Error; err != nil {
		return nil, fmt.Errorf("failed to get active customers: %w", err)
	}
	
	// Get blocked customers
	if err := r.db.WithContext(ctx).
		Model(&model.Customer{}).
		Where("tenant_id = ? AND status = ?", tenantID, model.CustomerStatusBlocked).
		Count(&stats.BlockedCustomers).Error; err != nil {
		return nil, fmt.Errorf("failed to get blocked customers: %w", err)
	}
	
	// Get gender stats
	var genderResults []struct {
		Gender string
		Count  int64
	}
	if err := r.db.WithContext(ctx).
		Model(&model.Customer{}).
		Select("gender, COUNT(*) as count").
		Where("tenant_id = ?", tenantID).
		Group("gender").
		Scan(&genderResults).Error; err != nil {
		return nil, fmt.Errorf("failed to get gender stats: %w", err)
	}
	
	for _, result := range genderResults {
		stats.GenderStats[result.Gender] = result.Count
	}
	
	// Get status stats
	var statusResults []struct {
		Status string
		Count  int64
	}
	if err := r.db.WithContext(ctx).
		Model(&model.Customer{}).
		Select("status, COUNT(*) as count").
		Where("tenant_id = ?", tenantID).
		Group("status").
		Scan(&statusResults).Error; err != nil {
		return nil, fmt.Errorf("failed to get status stats: %w", err)
	}
	
	for _, result := range statusResults {
		stats.StatusStats[result.Status] = result.Count
	}
	
	return stats, nil
}

// HasActivities checks if a customer has activities
func (r *customerRepository) HasActivities(ctx context.Context, customerID uint64) (bool, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&model.CustomerActivity{}).
		Where("customer_id = ?", customerID).
		Count(&count).Error
	
	if err != nil {
		return false, fmt.Errorf("failed to check customer activities: %w", err)
	}
	
	return count > 0, nil
}
