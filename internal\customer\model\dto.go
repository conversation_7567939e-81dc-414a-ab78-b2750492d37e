package model

import "time"

// CreateCustomerRequest represents a request to create a new customer
type CreateCustomerRequest struct {
	CustomerCode string                 `json:"customer_code" binding:"required,min=2,max=50"`
	FirstName    string                 `json:"first_name" binding:"required,min=1,max=50"`
	LastName     string                 `json:"last_name" binding:"required,min=1,max=50"`
	Email        string                 `json:"email" binding:"required,email"`
	Phone        string                 `json:"phone" binding:"required"`
	DateOfBirth  *time.Time             `json:"date_of_birth"`
	Gender       string                 `json:"gender" binding:"omitempty,oneof=male female other"`
	ProfileData  map[string]interface{} `json:"profile_data"`
	Tags         []string               `json:"tags"`
	TenantID     uint64                 `json:"tenant_id" binding:"required"`
}

// UpdateCustomerRequest represents a request to update a customer
type UpdateCustomerRequest struct {
	FirstName    string                 `json:"first_name" binding:"omitempty,min=1,max=50"`
	LastName     string                 `json:"last_name" binding:"omitempty,min=1,max=50"`
	Email        string                 `json:"email" binding:"omitempty,email"`
	Phone        string                 `json:"phone"`
	DateOfBirth  *time.Time             `json:"date_of_birth"`
	Gender       string                 `json:"gender" binding:"omitempty,oneof=male female other"`
	Status       *int8                  `json:"status" binding:"omitempty,oneof=0 1 2"`
	ProfileData  map[string]interface{} `json:"profile_data"`
	Tags         []string               `json:"tags"`
}

// CustomerResponse represents a customer response
type CustomerResponse struct {
	ID           uint64                 `json:"id"`
	CustomerCode string                 `json:"customer_code"`
	FirstName    string                 `json:"first_name"`
	LastName     string                 `json:"last_name"`
	FullName     string                 `json:"full_name"`
	Email        string                 `json:"email"`
	Phone        string                 `json:"phone"`
	DateOfBirth  *time.Time             `json:"date_of_birth"`
	Gender       string                 `json:"gender"`
	Status       int8                   `json:"status"`
	ProfileData  map[string]interface{} `json:"profile_data"`
	Tags         []string               `json:"tags"`
	Groups       []CustomerGroupInfo    `json:"groups"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// CustomerListQuery represents query parameters for listing customers
type CustomerListQuery struct {
	Page         int       `form:"page,default=1" binding:"min=1"`
	PageSize     int       `form:"page_size,default=20" binding:"min=1,max=100"`
	Status       *int8     `form:"status" binding:"omitempty,oneof=0 1 2"`
	Gender       string    `form:"gender" binding:"omitempty,oneof=male female other"`
	Search       string    `form:"search"`
	Tags         []string  `form:"tags"`
	Groups       []string  `form:"groups"`
	DateFrom     time.Time `form:"date_from" time_format:"2006-01-02"`
	DateTo       time.Time `form:"date_to" time_format:"2006-01-02"`
	TenantID     uint64    `form:"tenant_id"`
}

// CreateCustomerGroupRequest represents a request to create a customer group
type CreateCustomerGroupRequest struct {
	GroupCode   string                 `json:"group_code" binding:"required,min=2,max=50"`
	GroupName   string                 `json:"group_name" binding:"required,min=2,max=100"`
	Description string                 `json:"description"`
	Conditions  map[string]interface{} `json:"conditions"`
	TenantID    uint64                 `json:"tenant_id" binding:"required"`
}

// UpdateCustomerGroupRequest represents a request to update a customer group
type UpdateCustomerGroupRequest struct {
	GroupName   string                 `json:"group_name" binding:"omitempty,min=2,max=100"`
	Description string                 `json:"description"`
	Conditions  map[string]interface{} `json:"conditions"`
	Status      *int8                  `json:"status" binding:"omitempty,oneof=0 1"`
}

// CustomerGroupResponse represents a customer group response
type CustomerGroupResponse struct {
	ID          uint64                 `json:"id"`
	GroupCode   string                 `json:"group_code"`
	GroupName   string                 `json:"group_name"`
	Description string                 `json:"description"`
	Conditions  map[string]interface{} `json:"conditions"`
	Status      int8                   `json:"status"`
	MemberCount int64                  `json:"member_count"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// CustomerGroupInfo represents basic group information
type CustomerGroupInfo struct {
	ID        uint64    `json:"id"`
	GroupCode string    `json:"group_code"`
	GroupName string    `json:"group_name"`
	JoinedAt  time.Time `json:"joined_at"`
}

// CustomerGroupListQuery represents query parameters for listing customer groups
type CustomerGroupListQuery struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Status   *int8  `form:"status" binding:"omitempty,oneof=0 1"`
	Search   string `form:"search"`
	TenantID uint64 `form:"tenant_id"`
}

// AddCustomersToGroupRequest represents a request to add customers to a group
type AddCustomersToGroupRequest struct {
	CustomerIDs []uint64 `json:"customer_ids" binding:"required,min=1"`
}

// CreateCustomerTagRequest represents a request to create a customer tag
type CreateCustomerTagRequest struct {
	TagName  string `json:"tag_name" binding:"required,min=1,max=50"`
	TagColor string `json:"tag_color" binding:"omitempty,len=7"`
	TenantID uint64 `json:"tenant_id" binding:"required"`
}

// UpdateCustomerTagRequest represents a request to update a customer tag
type UpdateCustomerTagRequest struct {
	TagName  string `json:"tag_name" binding:"omitempty,min=1,max=50"`
	TagColor string `json:"tag_color" binding:"omitempty,len=7"`
	Status   *int8  `json:"status" binding:"omitempty,oneof=0 1"`
}

// CustomerTagResponse represents a customer tag response
type CustomerTagResponse struct {
	ID        uint64    `json:"id"`
	TagName   string    `json:"tag_name"`
	TagColor  string    `json:"tag_color"`
	Status    int8      `json:"status"`
	UsageCount int64    `json:"usage_count"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CustomerActivityResponse represents a customer activity response
type CustomerActivityResponse struct {
	ID           uint64                 `json:"id"`
	ActivityType string                 `json:"activity_type"`
	Description  string                 `json:"description"`
	Metadata     map[string]interface{} `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
}

// CustomerStatsResponse represents customer statistics
type CustomerStatsResponse struct {
	TotalCustomers   int64            `json:"total_customers"`
	ActiveCustomers  int64            `json:"active_customers"`
	BlockedCustomers int64            `json:"blocked_customers"`
	GenderStats      map[string]int64 `json:"gender_stats"`
	StatusStats      map[string]int64 `json:"status_stats"`
	RecentActivities int64            `json:"recent_activities"`
}

// ToCustomerResponse converts Customer model to CustomerResponse DTO
func (c *Customer) ToCustomerResponse() CustomerResponse {
	var profileData map[string]interface{}
	var tags []string
	var groups []CustomerGroupInfo

	// Parse JSON fields (simplified for demo)
	profileData = make(map[string]interface{})
	tags = []string{}

	// Convert groups
	for _, group := range c.Groups {
		groups = append(groups, CustomerGroupInfo{
			ID:        group.ID,
			GroupCode: group.GroupCode,
			GroupName: group.GroupName,
			// JoinedAt would need to be fetched from the join table
		})
	}

	return CustomerResponse{
		ID:           c.ID,
		CustomerCode: c.CustomerCode,
		FirstName:    c.FirstName,
		LastName:     c.LastName,
		FullName:     c.GetFullName(),
		Email:        c.Email,
		Phone:        c.Phone,
		DateOfBirth:  c.DateOfBirth,
		Gender:       c.Gender,
		Status:       c.Status,
		ProfileData:  profileData,
		Tags:         tags,
		Groups:       groups,
		CreatedAt:    c.CreatedAt,
		UpdatedAt:    c.UpdatedAt,
	}
}

// ToCustomerGroupResponse converts CustomerGroup model to CustomerGroupResponse DTO
func (g *CustomerGroup) ToCustomerGroupResponse() CustomerGroupResponse {
	var conditions map[string]interface{}
	// Parse JSON conditions (simplified for demo)
	conditions = make(map[string]interface{})

	return CustomerGroupResponse{
		ID:          g.ID,
		GroupCode:   g.GroupCode,
		GroupName:   g.GroupName,
		Description: g.Description,
		Conditions:  conditions,
		Status:      g.Status,
		// MemberCount would need to be calculated
		CreatedAt: g.CreatedAt,
		UpdatedAt: g.UpdatedAt,
	}
}

// ToCustomerTagResponse converts CustomerTag model to CustomerTagResponse DTO
func (t *CustomerTag) ToCustomerTagResponse() CustomerTagResponse {
	return CustomerTagResponse{
		ID:        t.ID,
		TagName:   t.TagName,
		TagColor:  t.TagColor,
		Status:    t.Status,
		// UsageCount would need to be calculated
		CreatedAt: t.CreatedAt,
		UpdatedAt: t.UpdatedAt,
	}
}

// Validate validates CreateCustomerRequest
func (r *CreateCustomerRequest) Validate() error {
	if r.Gender != "" && !ValidateGender(r.Gender) {
		return ErrInvalidGender
	}
	return nil
}

// Validate validates UpdateCustomerRequest
func (r *UpdateCustomerRequest) Validate() error {
	if r.Gender != "" && !ValidateGender(r.Gender) {
		return ErrInvalidGender
	}
	return nil
}
