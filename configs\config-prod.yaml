# CMHK Communication Platform - Production Configuration
server:
  port: 8080
  mode: production
  read_timeout: 30
  write_timeout: 30

database:
  mysql:
    host: ${MYSQL_HOST}
    port: ${MYSQL_PORT}
    username: ${MYSQL_USERNAME}
    password: ${MYSQL_PASSWORD}
    database: ${MYSQL_DATABASE}
    max_open_conns: 100
    max_idle_conns: 10
    max_lifetime: 3600
  
  redis:
    addresses:
      - ${REDIS_HOST_1}:${REDIS_PORT_1}
      - ${REDIS_HOST_2}:${REDIS_PORT_2}
      - ${REDIS_HOST_3}:${REDIS_PORT_3}
    password: ${REDIS_PASSWORD}
    db: 0
    pool_size: 100
  
  clickhouse:
    host: ${CLICKHOUSE_HOST}
    port: ${CLICKHOUSE_PORT}
    username: ${CLICKHOUSE_USERNAME}
    password: ${CLICKHOUSE_PASSWORD}
    database: ${CLICKHOUSE_DATABASE}

cache:
  default_ttl: 3600

auth:
  jwt_secret: ${JWT_SECRET}
  token_expiry: 3600
  refresh_expiry: 86400

external:
  sms:
    gateway: ${SMS_GATEWAY}
    api_key: ${SMS_API_KEY}
    secret: ${SMS_SECRET}
  
  whatsapp:
    business_api_url: ${WHATSAPP_API_URL}
    access_token: ${WHATSAPP_ACCESS_TOKEN}
    phone_number_id: ${WHATSAPP_PHONE_NUMBER_ID}
  
  wechat:
    app_id: ${WECHAT_APP_ID}
    app_secret: ${WECHAT_APP_SECRET}
