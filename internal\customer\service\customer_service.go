package service

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"cmhk-platform/internal/customer/model"
	"cmhk-platform/internal/customer/repository"
	"cmhk-platform/pkg/logger"
)

// CustomerService defines the interface for customer business logic
type CustomerService interface {
	CreateCustomer(ctx context.Context, req model.CreateCustomerRequest) (*model.Customer, error)
	GetCustomer(ctx context.Context, id uint64) (*model.Customer, error)
	UpdateCustomer(ctx context.Context, id uint64, req model.UpdateCustomerRequest) (*model.Customer, error)
	DeleteCustomer(ctx context.Context, id uint64) error
	ListCustomers(ctx context.Context, query model.CustomerListQuery) ([]*model.Customer, int64, error)
	GetCustomerByCode(ctx context.Context, tenantID uint64, code string) (*model.Customer, error)
	GetCustomerStats(ctx context.Context, tenantID uint64) (*model.CustomerStatsResponse, error)
	AddCustomerToGroup(ctx context.Context, customerID, groupID uint64) error
	RemoveCustomerFromGroup(ctx context.Context, customerID, groupID uint64) error
	LogActivity(ctx context.Context, customerID uint64, activityType, description string, metadata map[string]interface{}) error
}

// customerService implements CustomerService interface
type customerService struct {
	customerRepo repository.CustomerRepository
	groupRepo    repository.CustomerGroupRepository
	activityRepo repository.CustomerActivityRepository
	logger       *logger.Logger
}

// NewCustomerService creates a new customer service
func NewCustomerService(
	customerRepo repository.CustomerRepository,
	groupRepo repository.CustomerGroupRepository,
	activityRepo repository.CustomerActivityRepository,
	logger *logger.Logger,
) CustomerService {
	return &customerService{
		customerRepo: customerRepo,
		groupRepo:    groupRepo,
		activityRepo: activityRepo,
		logger:       logger,
	}
}

// CreateCustomer creates a new customer
func (s *customerService) CreateCustomer(ctx context.Context, req model.CreateCustomerRequest) (*model.Customer, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// Check if customer code already exists
	if _, err := s.customerRepo.GetByCode(ctx, req.TenantID, req.CustomerCode); err == nil {
		return nil, model.ErrCustomerAlreadyExists
	}

	// Check if email already exists
	if req.Email != "" {
		if _, err := s.customerRepo.GetByEmail(ctx, req.Email); err == nil {
			return nil, model.ErrCustomerAlreadyExists
		}
	}

	// Check if phone already exists
	if req.Phone != "" {
		if _, err := s.customerRepo.GetByPhone(ctx, req.Phone); err == nil {
			return nil, model.ErrCustomerAlreadyExists
		}
	}

	// Create customer
	customer := &model.Customer{
		TenantID:     req.TenantID,
		CustomerCode: req.CustomerCode,
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		Email:        req.Email,
		Phone:        req.Phone,
		DateOfBirth:  req.DateOfBirth,
		Gender:       req.Gender,
		Status:       model.CustomerStatusActive,
		// ProfileData and Tags would be JSON marshaled
	}

	if err := s.customerRepo.Create(ctx, customer); err != nil {
		return nil, fmt.Errorf("failed to create customer: %w", err)
	}

	// Log activity
	s.LogActivity(ctx, customer.ID, model.ActivityTypeRegistered, "Customer registered", nil)

	s.logger.Info("Customer created successfully", 
		zap.Uint64("customer_id", customer.ID),
		zap.String("customer_code", customer.CustomerCode))

	return customer, nil
}

// GetCustomer retrieves a customer by ID
func (s *customerService) GetCustomer(ctx context.Context, id uint64) (*model.Customer, error) {
	return s.customerRepo.GetByID(ctx, id)
}

// UpdateCustomer updates a customer
func (s *customerService) UpdateCustomer(ctx context.Context, id uint64, req model.UpdateCustomerRequest) (*model.Customer, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// Get existing customer
	customer, err := s.customerRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Check if email is being changed and already exists
	if req.Email != "" && req.Email != customer.Email {
		if _, err := s.customerRepo.GetByEmail(ctx, req.Email); err == nil {
			return nil, model.ErrCustomerAlreadyExists
		}
		customer.Email = req.Email
	}

	// Check if phone is being changed and already exists
	if req.Phone != "" && req.Phone != customer.Phone {
		if _, err := s.customerRepo.GetByPhone(ctx, req.Phone); err == nil {
			return nil, model.ErrCustomerAlreadyExists
		}
		customer.Phone = req.Phone
	}

	// Update fields
	if req.FirstName != "" {
		customer.FirstName = req.FirstName
	}
	if req.LastName != "" {
		customer.LastName = req.LastName
	}
	if req.DateOfBirth != nil {
		customer.DateOfBirth = req.DateOfBirth
	}
	if req.Gender != "" {
		customer.Gender = req.Gender
	}
	if req.Status != nil {
		customer.Status = *req.Status
	}

	// Save changes
	if err := s.customerRepo.Update(ctx, customer); err != nil {
		return nil, fmt.Errorf("failed to update customer: %w", err)
	}

	// Log activity
	s.LogActivity(ctx, customer.ID, model.ActivityTypeProfileUpdate, "Customer profile updated", nil)

	s.logger.Info("Customer updated successfully", 
		zap.Uint64("customer_id", customer.ID),
		zap.String("customer_code", customer.CustomerCode))

	return customer, nil
}

// DeleteCustomer soft deletes a customer
func (s *customerService) DeleteCustomer(ctx context.Context, id uint64) error {
	// Check if customer exists
	customer, err := s.customerRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// Check if customer has activities
	hasActivities, err := s.customerRepo.HasActivities(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check customer activities: %w", err)
	}

	if hasActivities {
		return model.ErrCustomerHasActivities
	}

	// Delete customer
	if err := s.customerRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete customer: %w", err)
	}

	s.logger.Info("Customer deleted successfully", 
		zap.Uint64("customer_id", customer.ID),
		zap.String("customer_code", customer.CustomerCode))

	return nil
}

// ListCustomers retrieves customers with pagination and filtering
func (s *customerService) ListCustomers(ctx context.Context, query model.CustomerListQuery) ([]*model.Customer, int64, error) {
	return s.customerRepo.List(ctx, query)
}

// GetCustomerByCode retrieves a customer by code
func (s *customerService) GetCustomerByCode(ctx context.Context, tenantID uint64, code string) (*model.Customer, error) {
	return s.customerRepo.GetByCode(ctx, tenantID, code)
}

// GetCustomerStats retrieves customer statistics
func (s *customerService) GetCustomerStats(ctx context.Context, tenantID uint64) (*model.CustomerStatsResponse, error) {
	return s.customerRepo.GetCustomerStats(ctx, tenantID)
}

// AddCustomerToGroup adds a customer to a group
func (s *customerService) AddCustomerToGroup(ctx context.Context, customerID, groupID uint64) error {
	// Check if customer exists
	customer, err := s.customerRepo.GetByID(ctx, customerID)
	if err != nil {
		return err
	}

	// Check if group exists
	group, err := s.groupRepo.GetByID(ctx, groupID)
	if err != nil {
		return err
	}

	if !group.IsActive() {
		return model.ErrGroupInactive
	}

	// Add to group
	if err := s.customerRepo.AddToGroup(ctx, customerID, groupID); err != nil {
		return fmt.Errorf("failed to add customer to group: %w", err)
	}

	// Log activity
	s.LogActivity(ctx, customerID, model.ActivityTypeGroupJoined, 
		fmt.Sprintf("Joined group: %s", group.GroupName), nil)

	s.logger.Info("Customer added to group", 
		zap.Uint64("customer_id", customerID),
		zap.Uint64("group_id", groupID))

	return nil
}

// RemoveCustomerFromGroup removes a customer from a group
func (s *customerService) RemoveCustomerFromGroup(ctx context.Context, customerID, groupID uint64) error {
	// Remove from group
	if err := s.customerRepo.RemoveFromGroup(ctx, customerID, groupID); err != nil {
		return fmt.Errorf("failed to remove customer from group: %w", err)
	}

	// Log activity
	s.LogActivity(ctx, customerID, model.ActivityTypeGroupLeft, 
		fmt.Sprintf("Left group ID: %d", groupID), nil)

	s.logger.Info("Customer removed from group", 
		zap.Uint64("customer_id", customerID),
		zap.Uint64("group_id", groupID))

	return nil
}

// LogActivity logs customer activity
func (s *customerService) LogActivity(ctx context.Context, customerID uint64, activityType, description string, metadata map[string]interface{}) error {
	if !model.ValidateActivityType(activityType) {
		return model.ErrInvalidActivityType
	}

	activity := &model.CustomerActivity{
		CustomerID:   customerID,
		ActivityType: activityType,
		Description:  description,
		// Metadata would be JSON marshaled
	}

	// Get tenant ID from customer
	customer, err := s.customerRepo.GetByID(ctx, customerID)
	if err == nil {
		activity.TenantID = customer.TenantID
	}

	if err := s.activityRepo.Create(ctx, activity); err != nil {
		s.logger.Error("Failed to log customer activity", 
			zap.Uint64("customer_id", customerID),
			zap.String("activity_type", activityType),
			zap.Error(err))
		// Don't return error as this is not critical
	}

	return nil
}
