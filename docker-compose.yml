version: '3.8'

services:
  # CMHK Platform Application
  cmhk-platform:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - CMHK_SERVER_MODE=development
      - CMHK_DATABASE_MYSQL_HOST=mysql
      - CMHK_DATABASE_MYSQL_PORT=3306
      - CMHK_DATABASE_MYSQL_USERNAME=cmhk
      - CMHK_DATABASE_MYSQL_PASSWORD=password
      - CMHK_DATABASE_MYSQL_DATABASE=cmhk_platform
      - CMHK_DATABASE_REDIS_ADDRESSES=redis:6379
      - CMHK_DATABASE_CLICKHOUSE_HOST=clickhouse
      - CMHK_DATABASE_CLICKHOUSE_PORT=9000
      - CMHK_AUTH_JWT_SECRET=docker-dev-secret-key
    depends_on:
      - mysql
      - redis
      - clickhouse
    restart: unless-stopped
    networks:
      - cmhk-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=cmhk_platform
      - MYSQL_USER=cmhk
      - MYSQL_PASSWORD=password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql-init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - cmhk-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - cmhk-network

  # ClickHouse Analytics Database
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    ports:
      - "9000:9000"
      - "8123:8123"
    environment:
      - CLICKHOUSE_DB=cmhk_analytics
      - CLICKHOUSE_USER=default
      - CLICKHOUSE_PASSWORD=
    volumes:
      - clickhouse_data:/var/lib/clickhouse
    restart: unless-stopped
    networks:
      - cmhk-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    networks:
      - cmhk-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped
    networks:
      - cmhk-network

volumes:
  mysql_data:
  redis_data:
  clickhouse_data:
  prometheus_data:
  grafana_data:

networks:
  cmhk-network:
    driver: bridge
