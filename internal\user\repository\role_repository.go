package repository

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"cmhk-platform/internal/user/model"
)

// RoleRepository defines the interface for role data access
type RoleRepository interface {
	Create(ctx context.Context, role *model.Role) error
	GetByID(ctx context.Context, id uint64) (*model.Role, error)
	GetByCode(ctx context.Context, tenantID uint64, code string) (*model.Role, error)
	Update(ctx context.Context, role *model.Role) error
	Delete(ctx context.Context, id uint64) error
	List(ctx context.Context, query model.RoleListQuery) ([]*model.Role, int64, error)
	HasUsersAssigned(ctx context.Context, roleID uint64) (bool, error)
}

// roleRepository implements RoleRepository interface
type roleRepository struct {
	db *gorm.DB
}

// NewRoleRepository creates a new role repository
func NewRoleRepository(db *gorm.DB) RoleRepository {
	return &roleRepository{db: db}
}

// Create creates a new role
func (r *roleRepository) Create(ctx context.Context, role *model.Role) error {
	if err := r.db.WithContext(ctx).Create(role).Error; err != nil {
		return fmt.Errorf("failed to create role: %w", err)
	}
	return nil
}

// GetByID retrieves a role by ID
func (r *roleRepository) GetByID(ctx context.Context, id uint64) (*model.Role, error) {
	var role model.Role
	err := r.db.WithContext(ctx).First(&role, id).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrRoleNotFound
		}
		return nil, fmt.Errorf("failed to get role by ID: %w", err)
	}
	
	return &role, nil
}

// GetByCode retrieves a role by tenant ID and role code
func (r *roleRepository) GetByCode(ctx context.Context, tenantID uint64, code string) (*model.Role, error) {
	var role model.Role
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND role_code = ?", tenantID, code).
		First(&role).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, model.ErrRoleNotFound
		}
		return nil, fmt.Errorf("failed to get role by code: %w", err)
	}
	
	return &role, nil
}

// Update updates a role
func (r *roleRepository) Update(ctx context.Context, role *model.Role) error {
	if err := r.db.WithContext(ctx).Save(role).Error; err != nil {
		return fmt.Errorf("failed to update role: %w", err)
	}
	return nil
}

// Delete soft deletes a role
func (r *roleRepository) Delete(ctx context.Context, id uint64) error {
	if err := r.db.WithContext(ctx).Delete(&model.Role{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete role: %w", err)
	}
	return nil
}

// List retrieves roles with pagination and filtering
func (r *roleRepository) List(ctx context.Context, query model.RoleListQuery) ([]*model.Role, int64, error) {
	var roles []*model.Role
	var total int64
	
	db := r.db.WithContext(ctx).Model(&model.Role{})
	
	// Apply filters
	if query.TenantID > 0 {
		db = db.Where("tenant_id = ?", query.TenantID)
	}
	
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}
	
	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count roles: %w", err)
	}
	
	// Apply pagination
	offset := (query.Page - 1) * query.PageSize
	if err := db.Offset(offset).
		Limit(query.PageSize).
		Find(&roles).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list roles: %w", err)
	}
	
	return roles, total, nil
}

// HasUsersAssigned checks if a role has users assigned to it
func (r *roleRepository) HasUsersAssigned(ctx context.Context, roleID uint64) (bool, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&model.UserRole{}).
		Where("role_id = ?", roleID).
		Count(&count).Error
	
	if err != nil {
		return false, fmt.Errorf("failed to check role assignments: %w", err)
	}
	
	return count > 0, nil
}
