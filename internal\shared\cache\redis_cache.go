package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisCache implements Cache interface using Redis
type RedisCache struct {
	client redis.Cmdable
}

// NewRedisCache creates a new Redis cache instance
func NewRedisCache(client redis.Cmdable) *RedisCache {
	return &RedisCache{
		client: client,
	}
}

// Get retrieves a value from Redis
func (rc *RedisCache) Get(ctx context.Context, key string) (string, error) {
	result, err := rc.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return "", fmt.Errorf("key not found: %s", key)
	}
	return result, err
}

// Set stores a value in Redis
func (rc *RedisCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return rc.client.Set(ctx, key, value, expiration).Err()
}

// Delete removes a value from Redis
func (rc *RedisCache) Delete(ctx context.Context, key string) error {
	return rc.client.Del(ctx, key).Err()
}

// Exists checks if a key exists in Redis
func (rc *RedisCache) Exists(ctx context.Context, key string) (bool, error) {
	result, err := rc.client.Exists(ctx, key).Result()
	return result > 0, err
}

// Expire sets expiration time for a key
func (rc *RedisCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return rc.client.Expire(ctx, key, expiration).Err()
}

// GetJSON retrieves and unmarshals JSON data from Redis
func (rc *RedisCache) GetJSON(ctx context.Context, key string, dest interface{}) error {
	jsonStr, err := rc.Get(ctx, key)
	if err != nil {
		return err
	}
	
	return json.Unmarshal([]byte(jsonStr), dest)
}

// SetJSON marshals and stores JSON data in Redis
func (rc *RedisCache) SetJSON(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	jsonBytes, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}
	
	return rc.Set(ctx, key, string(jsonBytes), expiration)
}

// GetMultiple retrieves multiple values from Redis
func (rc *RedisCache) GetMultiple(ctx context.Context, keys []string) (map[string]string, error) {
	if len(keys) == 0 {
		return make(map[string]string), nil
	}
	
	results, err := rc.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}
	
	data := make(map[string]string)
	for i, result := range results {
		if result != nil {
			data[keys[i]] = result.(string)
		}
	}
	
	return data, nil
}

// SetMultiple stores multiple values in Redis
func (rc *RedisCache) SetMultiple(ctx context.Context, data map[string]interface{}, expiration time.Duration) error {
	pipe := rc.client.Pipeline()
	
	for key, value := range data {
		pipe.Set(ctx, key, value, expiration)
	}
	
	_, err := pipe.Exec(ctx)
	return err
}

// DeleteMultiple removes multiple values from Redis
func (rc *RedisCache) DeleteMultiple(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}
	
	return rc.client.Del(ctx, keys...).Err()
}

// FlushAll removes all keys from Redis database
func (rc *RedisCache) FlushAll(ctx context.Context) error {
	return rc.client.FlushDB(ctx).Err()
}

// Increment increments a numeric value in Redis
func (rc *RedisCache) Increment(ctx context.Context, key string) (int64, error) {
	return rc.client.Incr(ctx, key).Result()
}

// IncrementBy increments a numeric value by specified amount
func (rc *RedisCache) IncrementBy(ctx context.Context, key string, value int64) (int64, error) {
	return rc.client.IncrBy(ctx, key, value).Result()
}

// Decrement decrements a numeric value in Redis
func (rc *RedisCache) Decrement(ctx context.Context, key string) (int64, error) {
	return rc.client.Decr(ctx, key).Result()
}

// DecrementBy decrements a numeric value by specified amount
func (rc *RedisCache) DecrementBy(ctx context.Context, key string, value int64) (int64, error) {
	return rc.client.DecrBy(ctx, key, value).Result()
}

// SetNX sets a value only if the key does not exist
func (rc *RedisCache) SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	return rc.client.SetNX(ctx, key, value, expiration).Result()
}

// GetTTL returns the remaining time to live of a key
func (rc *RedisCache) GetTTL(ctx context.Context, key string) (time.Duration, error) {
	return rc.client.TTL(ctx, key).Result()
}
