<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="企业级短链接管理平台架构图（层级标题优化版）" id="architecture-title-optimized">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1800" pageHeight="1500" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 用户层 -->
        <mxCell id="users-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="50" width="1600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="users-title" value="👥 用户层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1976d2;strokeColor=#1976d2;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="60" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="enterprise-user" value="企业用户&lt;br&gt;营销团队" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="300" y="90" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="developer" value="开发者&lt;br&gt;API用户" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="840" y="90" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="end-user" value="最终用户&lt;br&gt;链接访问者" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1380" y="90" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- 客户端层 -->
        <mxCell id="client-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="180" width="1600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="client-title" value="💻 客户端层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7b1fa2;strokeColor=#7b1fa2;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="190" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="web-console" value="Web管理后台&lt;br&gt;• 链接管理 • 数据分析&lt;br&gt;• 团队管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="270" y="220" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="api-interface" value="RESTful API&lt;br&gt;• 链接CRUD • 批量操作&lt;br&gt;• 数据查询" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="810" y="220" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="mobile-app" value="移动应用&lt;br&gt;• 浏览器插件&lt;br&gt;• 移动端管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1350" y="220" width="180" height="50" as="geometry" />
        </mxCell>

        <!-- API网关层 -->
        <mxCell id="gateway-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="310" width="1600" height="80" as="geometry" />
        </mxCell>
        <mxCell id="gateway-title" value="🚪 API网关层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f57c00;strokeColor=#f57c00;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="320" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="api-gateway" value="API Gateway • 身份认证 • 流量控制 • 请求路由 • 速率限制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="600" y="345" width="600" height="40" as="geometry" />
        </mxCell>

        <!-- 应用服务层 -->
        <mxCell id="services-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="420" width="1600" height="240" as="geometry" />
        </mxCell>
        <mxCell id="services-title" value="⚙️ 应用服务层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6c757d;strokeColor=#6c757d;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="430" width="140" height="30" as="geometry" />
        </mxCell>
        
        <!-- 核心服务行 -->
        <mxCell id="core-row-bg" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;strokeWidth=1;opacity=40;" vertex="1" parent="1">
          <mxGeometry x="150" y="470" width="1500" height="60" as="geometry" />
        </mxCell>
        <mxCell id="core-title" value="核心服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#388e3c;strokeColor=#388e3c;fontColor=#ffffff;fontStyle=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="160" y="475" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="id-service" value="ID生成服务&lt;br&gt;Snowflake • Base62" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="350" y="480" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="redirect-service" value="重定向服务&lt;br&gt;302跳转 • 高并发" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#388e3c;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1300" y="480" width="150" height="40" as="geometry" />
        </mxCell>

        <!-- 业务服务行 -->
        <mxCell id="business-row-bg" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#fbc02d;strokeWidth=1;opacity=40;" vertex="1" parent="1">
          <mxGeometry x="150" y="540" width="1500" height="60" as="geometry" />
        </mxCell>
        <mxCell id="business-title" value="业务服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fbc02d;strokeColor=#fbc02d;fontColor=#ffffff;fontStyle=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="160" y="545" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="mgmt-service" value="管理服务&lt;br&gt;链接 • 用户 • 权限" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#fbc02d;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="250" y="550" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="analytics-service" value="分析服务&lt;br&gt;统计 • 地理 • 设备" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#fbc02d;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="825" y="550" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="qr-service" value="二维码服务&lt;br&gt;静态码 • 活码" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff8e1;strokeColor=#fbc02d;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1400" y="550" width="150" height="40" as="geometry" />
        </mxCell>

        <!-- 高级服务行 -->
        <mxCell id="advanced-row-bg" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;strokeWidth=1;opacity=40;" vertex="1" parent="1">
          <mxGeometry x="150" y="610" width="1500" height="60" as="geometry" />
        </mxCell>
        <mxCell id="advanced-title" value="高级服务" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c2185b;strokeColor=#c2185b;fontColor=#ffffff;fontStyle=1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="160" y="615" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="ab-service" value="A/B测试&lt;br&gt;流量分配 • 效果对比" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="350" y="620" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="risk-service" value="风控服务&lt;br&gt;内容审核 • 行为监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="825" y="620" width="150" height="40" as="geometry" />
        </mxCell>
        <mxCell id="notify-service" value="通知服务&lt;br&gt;邮件 • 短信 • Webhook" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1300" y="620" width="150" height="40" as="geometry" />
        </mxCell>

        <!-- 缓存层（优化版） -->
        <mxCell id="cache-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00796b;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="690" width="1600" height="120" as="geometry" />
        </mxCell>
        <mxCell id="cache-title" value="🚀 缓存层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#00796b;strokeColor=#00796b;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="700" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="hot-cache" value="热点链接缓存&lt;br&gt;• URL映射缓存&lt;br&gt;• 访问频率优化&lt;br&gt;• LRU淘汰策略" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00796b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="220" y="740" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="session-store" value="会话存储&lt;br&gt;• 用户登录状态&lt;br&gt;• API访问令牌&lt;br&gt;• 临时数据存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00796b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="620" y="740" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="counter-module" value="计数器模块&lt;br&gt;• 点击次数统计&lt;br&gt;• 实时访问计数&lt;br&gt;• 限流计数器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00796b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1020" y="740" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="distributed-lock" value="分布式锁&lt;br&gt;• 并发控制&lt;br&gt;• 资源互斥访问&lt;br&gt;• 防重复操作" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e0f2f1;strokeColor=#00796b;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1420" y="740" width="180" height="60" as="geometry" />
        </mxCell>

        <!-- 数据存储层 -->
        <mxCell id="storage-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#689f38;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="840" width="1600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="storage-title" value="💾 数据存储层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#689f38;strokeColor=#689f38;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="850" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="nosql-db" value="NoSQL数据库&lt;br&gt;MongoDB/DynamoDB&lt;br&gt;• URL映射 • 点击日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#689f38;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="300" y="880" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="rdbms" value="关系型数据库&lt;br&gt;PostgreSQL/MySQL&lt;br&gt;• 用户信息 • 权限配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#689f38;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="810" y="880" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="file-storage" value="文件存储&lt;br&gt;• 二维码图片&lt;br&gt;• 导出文件 • 日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#689f38;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1320" y="880" width="180" height="50" as="geometry" />
        </mxCell>

        <!-- 外部服务层 -->
        <mxCell id="external-layer" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="100" y="970" width="1600" height="100" as="geometry" />
        </mxCell>
        <mxCell id="external-title" value="🌐 外部服务层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#616161;strokeColor=#616161;fontColor=#ffffff;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="980" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="security-api" value="安全检测API&lt;br&gt;• Safe Browsing&lt;br&gt;• 恶意链接检测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="250" y="1010" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="geo-api" value="地理位置API&lt;br&gt;• IP地理定位&lt;br&gt;• 城市级精度" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="650" y="1010" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="sms-service" value="短信服务&lt;br&gt;• 验证码发送&lt;br&gt;• 营销短信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1050" y="1010" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="monitor-service" value="监控服务&lt;br&gt;• 系统监控&lt;br&gt;• 日志收集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fafafa;strokeColor=#616161;fontStyle=0;" vertex="1" parent="1">
          <mxGeometry x="1450" y="1010" width="150" height="50" as="geometry" />
        </mxCell>

        <!-- 优化的连接线 - 垂直主流 -->
        <!-- 用户到客户端的连接 -->
        <mxCell id="conn-eu-web" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#1976d2;" edge="1" parent="1" source="enterprise-user" target="web-console">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn-dev-api" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#1976d2;" edge="1" parent="1" source="developer" target="api-interface">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn-end-redirect" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#1976d2;" edge="1" parent="1" source="end-user" target="redirect-service">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1540" y="160" />
              <mxPoint x="1540" y="400" />
              <mxPoint x="1475" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 客户端到网关的连接 -->
        <mxCell id="conn-web-gateway" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#7b1fa2;" edge="1" parent="1" source="web-console" target="api-gateway">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn-api-gateway" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#7b1fa2;" edge="1" parent="1" source="api-interface" target="api-gateway">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn-mobile-gateway" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#7b1fa2;" edge="1" parent="1" source="mobile-app" target="api-gateway">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 网关到服务的连接 -->
        <mxCell id="conn-gateway-mgmt" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#f57c00;" edge="1" parent="1" source="api-gateway" target="mgmt-service">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="900" y="400" />
              <mxPoint x="425" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-gateway-analytics" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#f57c00;" edge="1" parent="1" source="api-gateway" target="analytics-service">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn-gateway-qr" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#f57c00;" edge="1" parent="1" source="api-gateway" target="qr-service">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="900" y="400" />
              <mxPoint x="1575" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 核心服务连接 -->
        <mxCell id="conn-id-mgmt" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#388e3c;" edge="1" parent="1" source="id-service" target="mgmt-service">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 重定向服务到缓存模块的连接（优化版） -->
        <mxCell id="conn-redirect-hotcache" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#388e3c;" edge="1" parent="1" source="redirect-service" target="hot-cache">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1475" y="680" />
              <mxPoint x="410" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-redirect-counter" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#388e3c;" edge="1" parent="1" source="redirect-service" target="counter-module">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1475" y="680" />
              <mxPoint x="1210" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-redirect-lock" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#388e3c;" edge="1" parent="1" source="redirect-service" target="distributed-lock">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1475" y="680" />
              <mxPoint x="1610" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-redirect-analytics" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#388e3c;" edge="1" parent="1" source="redirect-service" target="analytics-service">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1475" y="530" />
              <mxPoint x="1000" y="530" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- API网关到缓存模块的连接 -->
        <mxCell id="conn-gateway-session" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#f57c00;" edge="1" parent="1" source="api-gateway" target="session-store">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="900" y="680" />
              <mxPoint x="810" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-gateway-counter" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#f57c00;" edge="1" parent="1" source="api-gateway" target="counter-module">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="900" y="680" />
              <mxPoint x="1210" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 业务服务连接 -->
        <mxCell id="conn-mgmt-nosql" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#fbc02d;" edge="1" parent="1" source="mgmt-service" target="nosql-db">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="conn-mgmt-rdbms" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#fbc02d;" edge="1" parent="1" source="mgmt-service" target="rdbms">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="425" y="860" />
              <mxPoint x="1000" y="860" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-mgmt-session" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#fbc02d;" edge="1" parent="1" source="mgmt-service" target="session-store">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="425" y="680" />
              <mxPoint x="810" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-analytics-nosql" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#fbc02d;" edge="1" parent="1" source="analytics-service" target="nosql-db">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1000" y="860" />
              <mxPoint x="490" y="860" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-analytics-counter" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#fbc02d;" edge="1" parent="1" source="analytics-service" target="counter-module">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1000" y="680" />
              <mxPoint x="1210" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-analytics-geo" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#fbc02d;" edge="1" parent="1" source="analytics-service" target="geo-api">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1000" y="990" />
              <mxPoint x="825" y="990" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-qr-files" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#fbc02d;" edge="1" parent="1" source="qr-service" target="file-storage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 高级服务连接 -->
        <mxCell id="conn-ab-lock" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#c2185b;" edge="1" parent="1" source="ab-service" target="distributed-lock">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="525" y="680" />
              <mxPoint x="1610" y="680" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-risk-security" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#c2185b;" edge="1" parent="1" source="risk-service" target="security-api">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1000" y="990" />
              <mxPoint x="425" y="990" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-notify-sms" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#c2185b;" edge="1" parent="1" source="notify-service" target="sms-service">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1475" y="990" />
              <mxPoint x="1225" y="990" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 缓存模块到存储的连接 -->
        <mxCell id="conn-hotcache-nosql" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#00796b;" edge="1" parent="1" source="hot-cache" target="nosql-db">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="410" y="860" />
              <mxPoint x="490" y="860" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-session-rdbms" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#00796b;" edge="1" parent="1" source="session-store" target="rdbms">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="810" y="860" />
              <mxPoint x="1000" y="860" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="conn-counter-analytics" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#00796b;" edge="1" parent="1" source="counter-module" target="analytics-service">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1210" y="630" />
              <mxPoint x="1000" y="630" />
            </Array>
          </mxGeometry>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
