package model

import "time"

// CreateUserRequest represents a request to create a new user
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Phone    string `json:"phone" binding:"required"`
	Password string `json:"password" binding:"required,min=8"`
	TenantID uint64 `json:"tenant_id" binding:"required"`
}

// UpdateUserRequest represents a request to update a user
type UpdateUserRequest struct {
	Email  string `json:"email" binding:"omitempty,email"`
	Phone  string `json:"phone"`
	Status int8   `json:"status" binding:"omitempty,oneof=0 1 2 3"`
}

// ChangePasswordRequest represents a request to change user password
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
}

// LoginRequest represents a login request
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	User         UserInfo  `json:"user"`
}

// RefreshTokenRequest represents a refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// UserInfo represents user information for responses
type UserInfo struct {
	ID       uint64   `json:"id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Phone    string   `json:"phone"`
	Status   int8     `json:"status"`
	TenantID uint64   `json:"tenant_id"`
	Roles    []string `json:"roles"`
}

// CreateRoleRequest represents a request to create a new role
type CreateRoleRequest struct {
	RoleCode    string   `json:"role_code" binding:"required,min=2,max=50"`
	RoleName    string   `json:"role_name" binding:"required,min=2,max=100"`
	Permissions []string `json:"permissions" binding:"required"`
	TenantID    uint64   `json:"tenant_id" binding:"required"`
}

// UpdateRoleRequest represents a request to update a role
type UpdateRoleRequest struct {
	RoleName    string   `json:"role_name" binding:"omitempty,min=2,max=100"`
	Permissions []string `json:"permissions"`
	Status      int8     `json:"status" binding:"omitempty,oneof=0 1"`
}

// AssignRoleRequest represents a request to assign a role to a user
type AssignRoleRequest struct {
	UserID    uint64     `json:"user_id" binding:"required"`
	RoleID    uint64     `json:"role_id" binding:"required"`
	ExpiresAt *time.Time `json:"expires_at"`
}

// UserListQuery represents query parameters for listing users
type UserListQuery struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Status   *int8  `form:"status" binding:"omitempty,oneof=0 1 2 3"`
	Search   string `form:"search"`
	TenantID uint64 `form:"tenant_id"`
}

// RoleListQuery represents query parameters for listing roles
type RoleListQuery struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Status   *int8  `form:"status" binding:"omitempty,oneof=0 1"`
	TenantID uint64 `form:"tenant_id"`
}

// ToUserInfo converts User model to UserInfo DTO
func (u *User) ToUserInfo() UserInfo {
	return UserInfo{
		ID:       u.ID,
		Username: u.Username,
		Email:    u.Email,
		Phone:    u.Phone,
		Status:   u.Status,
		TenantID: u.TenantID,
		Roles:    u.GetRoleCodes(),
	}
}

// Validate validates CreateUserRequest
func (r *CreateUserRequest) Validate() error {
	// Additional validation logic can be added here
	return nil
}

// Validate validates UpdateUserRequest
func (r *UpdateUserRequest) Validate() error {
	// Additional validation logic can be added here
	return nil
}

// Validate validates ChangePasswordRequest
func (r *ChangePasswordRequest) Validate() error {
	// Additional validation logic can be added here
	if r.CurrentPassword == r.NewPassword {
		return ErrSamePassword
	}
	return nil
}
