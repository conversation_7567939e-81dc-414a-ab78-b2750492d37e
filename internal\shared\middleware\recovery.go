package middleware

import (
	"fmt"
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"cmhk-platform/internal/shared/types"
	"cmhk-platform/pkg/logger"
)

// RecoveryConfig represents recovery middleware configuration
type RecoveryConfig struct {
	Logger        *logger.Logger
	EnableStack   bool
	CustomHandler func(*gin.Context, interface{})
}

// Recovery returns a recovery middleware with default configuration
func Recovery(log *logger.Logger) gin.HandlerFunc {
	return RecoveryWithConfig(RecoveryConfig{
		Logger:      log,
		EnableStack: true,
	})
}

// RecoveryWithConfig returns a recovery middleware with custom configuration
func RecoveryWithConfig(config RecoveryConfig) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if config.CustomHandler != nil {
			config.CustomHandler(c, recovered)
			return
		}

		// Get request ID
		requestID := getRequestIDFromContext(c)

		// Log the panic
		fields := []zap.Field{
			zap.String("method", c.Request.Method),
			zap.String("path", c.Request.URL.Path),
			zap.String("client_ip", c.ClientIP()),
			zap.Any("panic", recovered),
		}

		if requestID != "" {
			fields = append(fields, zap.String("request_id", requestID))
		}

		if config.EnableStack {
			fields = append(fields, zap.String("stack", string(debug.Stack())))
		}

		config.Logger.Error("Panic recovered", fields...)

		// Return error response
		errorMessage := "Internal server error"
		if err, ok := recovered.(error); ok {
			errorMessage = err.Error()
		} else if str, ok := recovered.(string); ok {
			errorMessage = str
		} else {
			errorMessage = fmt.Sprintf("%v", recovered)
		}

		types.ErrorResponse(c, http.StatusInternalServerError, 
			types.ErrorCodeInternalError, errorMessage)
	})
}

// getRequestIDFromContext extracts request ID from gin context
func getRequestIDFromContext(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}
