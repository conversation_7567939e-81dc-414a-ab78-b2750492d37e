package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"cmhk-platform/internal/shared/types"
	"cmhk-platform/pkg/logger"
)

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	// Global rate limits
	GlobalRPS     int           `yaml:"global_rps" mapstructure:"global_rps"`
	GlobalBurst   int           `yaml:"global_burst" mapstructure:"global_burst"`
	GlobalWindow  time.Duration `yaml:"global_window" mapstructure:"global_window"`
	
	// Per-IP rate limits
	PerIPRPS      int           `yaml:"per_ip_rps" mapstructure:"per_ip_rps"`
	PerIPBurst    int           `yaml:"per_ip_burst" mapstructure:"per_ip_burst"`
	PerIPWindow   time.Duration `yaml:"per_ip_window" mapstructure:"per_ip_window"`
	
	// Per-User rate limits
	PerUserRPS    int           `yaml:"per_user_rps" mapstructure:"per_user_rps"`
	PerUserBurst  int           `yaml:"per_user_burst" mapstructure:"per_user_burst"`
	PerUserWindow time.Duration `yaml:"per_user_window" mapstructure:"per_user_window"`
	
	// API-specific rate limits
	APILimits     map[string]APILimit `yaml:"api_limits" mapstructure:"api_limits"`
	
	// Redis settings
	RedisKeyPrefix string        `yaml:"redis_key_prefix" mapstructure:"redis_key_prefix"`
	RedisKeyTTL    time.Duration `yaml:"redis_key_ttl" mapstructure:"redis_key_ttl"`
	
	// Response settings
	IncludeHeaders bool `yaml:"include_headers" mapstructure:"include_headers"`
	
	// Whitelist/Blacklist
	WhitelistIPs []string `yaml:"whitelist_ips" mapstructure:"whitelist_ips"`
	BlacklistIPs []string `yaml:"blacklist_ips" mapstructure:"blacklist_ips"`
}

// APILimit defines rate limits for specific API endpoints
type APILimit struct {
	RPS    int           `yaml:"rps" mapstructure:"rps"`
	Burst  int           `yaml:"burst" mapstructure:"burst"`
	Window time.Duration `yaml:"window" mapstructure:"window"`
}

// RateLimiter implements rate limiting using Redis
type RateLimiter struct {
	redis  *redis.Client
	config *RateLimitConfig
	logger *logger.Logger
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(redis *redis.Client, config *RateLimitConfig, logger *logger.Logger) *RateLimiter {
	if config == nil {
		config = DefaultRateLimitConfig()
	}
	
	return &RateLimiter{
		redis:  redis,
		config: config,
		logger: logger,
	}
}

// DefaultRateLimitConfig returns default rate limiting configuration
func DefaultRateLimitConfig() *RateLimitConfig {
	return &RateLimitConfig{
		GlobalRPS:      1000,
		GlobalBurst:    100,
		GlobalWindow:   time.Minute,
		PerIPRPS:       100,
		PerIPBurst:     20,
		PerIPWindow:    time.Minute,
		PerUserRPS:     200,
		PerUserBurst:   50,
		PerUserWindow:  time.Minute,
		RedisKeyPrefix: "ratelimit:",
		RedisKeyTTL:    time.Minute * 2,
		IncludeHeaders: true,
		APILimits: map[string]APILimit{
			"/api/v1/auth/login": {
				RPS:    10,
				Burst:  5,
				Window: time.Minute,
			},
			"/api/v1/communication/messages": {
				RPS:    50,
				Burst:  10,
				Window: time.Minute,
			},
		},
		WhitelistIPs: []string{},
		BlacklistIPs: []string{},
	}
}

// Middleware returns a Gin middleware for rate limiting
func (rl *RateLimiter) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check blacklist first
		clientIP := c.ClientIP()
		if rl.isBlacklisted(clientIP) {
			rl.logger.Warn("Blocked blacklisted IP", zap.String("ip", clientIP))
			types.ErrorResponse(c, http.StatusForbidden, types.ErrorCodeForbidden, "Access denied")
			c.Abort()
			return
		}
		
		// Check whitelist
		if rl.isWhitelisted(clientIP) {
			c.Next()
			return
		}
		
		// Apply rate limiting
		allowed, limit, remaining, resetTime, err := rl.checkRateLimit(c)
		if err != nil {
			rl.logger.Error("Rate limit check failed", zap.Error(err))
			// Continue on error to avoid blocking legitimate requests
			c.Next()
			return
		}
		
		// Add rate limit headers
		if rl.config.IncludeHeaders {
			c.Header("X-RateLimit-Limit", strconv.Itoa(limit))
			c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
			c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime.Unix(), 10))
		}
		
		if !allowed {
			rl.logger.Warn("Rate limit exceeded",
				zap.String("ip", clientIP),
				zap.String("path", c.Request.URL.Path),
				zap.Int("limit", limit),
				zap.Int("remaining", remaining))
			
			c.Header("Retry-After", strconv.Itoa(int(time.Until(resetTime).Seconds())))
			types.ErrorResponse(c, http.StatusTooManyRequests, types.ErrorCodeRateLimitExceeded, "Rate limit exceeded")
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// checkRateLimit checks if the request should be rate limited
func (rl *RateLimiter) checkRateLimit(c *gin.Context) (allowed bool, limit int, remaining int, resetTime time.Time, err error) {
	ctx := c.Request.Context()
	clientIP := c.ClientIP()
	path := c.Request.URL.Path
	userID := rl.getUserID(c)
	
	// Check API-specific limits first
	if apiLimit, exists := rl.config.APILimits[path]; exists {
		key := fmt.Sprintf("%sapi:%s:%s", rl.config.RedisKeyPrefix, path, clientIP)
		return rl.checkLimit(ctx, key, apiLimit.RPS, apiLimit.Burst, apiLimit.Window)
	}
	
	// Check per-user limits
	if userID != "" {
		key := fmt.Sprintf("%suser:%s", rl.config.RedisKeyPrefix, userID)
		allowed, limit, remaining, resetTime, err = rl.checkLimit(ctx, key, rl.config.PerUserRPS, rl.config.PerUserBurst, rl.config.PerUserWindow)
		if err != nil || !allowed {
			return
		}
	}
	
	// Check per-IP limits
	key := fmt.Sprintf("%sip:%s", rl.config.RedisKeyPrefix, clientIP)
	allowed, limit, remaining, resetTime, err = rl.checkLimit(ctx, key, rl.config.PerIPRPS, rl.config.PerIPBurst, rl.config.PerIPWindow)
	if err != nil || !allowed {
		return
	}
	
	// Check global limits
	key = fmt.Sprintf("%sglobal", rl.config.RedisKeyPrefix)
	return rl.checkLimit(ctx, key, rl.config.GlobalRPS, rl.config.GlobalBurst, rl.config.GlobalWindow)
}

// checkLimit implements sliding window rate limiting using Redis
func (rl *RateLimiter) checkLimit(ctx context.Context, key string, rps, burst int, window time.Duration) (allowed bool, limit int, remaining int, resetTime time.Time, err error) {
	now := time.Now()
	windowStart := now.Truncate(window)
	resetTime = windowStart.Add(window)
	
	// Use Redis pipeline for atomic operations
	pipe := rl.redis.Pipeline()
	
	// Get current count
	countCmd := pipe.Get(ctx, key)
	
	// Set expiration
	pipe.Expire(ctx, key, rl.config.RedisKeyTTL)
	
	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return false, rps, 0, resetTime, fmt.Errorf("failed to get rate limit count: %w", err)
	}
	
	currentCount := 0
	if err != redis.Nil {
		currentCount, _ = strconv.Atoi(countCmd.Val())
	}
	
	// Check if limit exceeded
	if currentCount >= rps {
		return false, rps, 0, resetTime, nil
	}
	
	// Increment counter
	newCount, err := rl.redis.Incr(ctx, key).Result()
	if err != nil {
		return false, rps, 0, resetTime, fmt.Errorf("failed to increment rate limit counter: %w", err)
	}
	
	// Set expiration if this is the first request in the window
	if newCount == 1 {
		rl.redis.Expire(ctx, key, window)
	}
	
	remaining = rps - int(newCount)
	if remaining < 0 {
		remaining = 0
	}
	
	return newCount <= int64(rps), rps, remaining, resetTime, nil
}

// isWhitelisted checks if an IP is whitelisted
func (rl *RateLimiter) isWhitelisted(ip string) bool {
	for _, whiteIP := range rl.config.WhitelistIPs {
		if ip == whiteIP {
			return true
		}
	}
	return false
}

// isBlacklisted checks if an IP is blacklisted
func (rl *RateLimiter) isBlacklisted(ip string) bool {
	for _, blackIP := range rl.config.BlacklistIPs {
		if ip == blackIP {
			return true
		}
	}
	return false
}

// getUserID extracts user ID from the request context
func (rl *RateLimiter) getUserID(c *gin.Context) string {
	// Try to get user ID from JWT claims
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint64); ok {
			return strconv.FormatUint(id, 10)
		}
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}

// GetStats returns rate limiting statistics
func (rl *RateLimiter) GetStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// Get Redis info
	info, err := rl.redis.Info(ctx, "stats").Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get Redis stats: %w", err)
	}
	
	stats["redis_info"] = info
	stats["config"] = rl.config
	
	return stats, nil
}

// CleanupExpiredKeys removes expired rate limit keys (optional maintenance)
func (rl *RateLimiter) CleanupExpiredKeys(ctx context.Context) error {
	pattern := rl.config.RedisKeyPrefix + "*"
	keys, err := rl.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get rate limit keys: %w", err)
	}
	
	// Check TTL for each key and remove expired ones
	for _, key := range keys {
		ttl, err := rl.redis.TTL(ctx, key).Result()
		if err != nil {
			continue
		}
		
		// Remove keys with no TTL or negative TTL
		if ttl <= 0 {
			rl.redis.Del(ctx, key)
		}
	}
	
	return nil
}
