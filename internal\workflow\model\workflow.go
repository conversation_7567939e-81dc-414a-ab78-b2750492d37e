package model

import (
	"time"
	"gorm.io/gorm"
)

// Workflow represents a workflow definition
type Workflow struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_code" json:"tenant_id"`
	WorkflowCode string        `gorm:"size:50;not null;uniqueIndex:uk_tenant_workflow_code" json:"workflow_code"`
	WorkflowName string        `gorm:"size:100;not null" json:"workflow_name"`
	Description string         `gorm:"type:text" json:"description"`
	Category    string         `gorm:"size:50;not null" json:"category"`
	Version     string         `gorm:"size:20;not null" json:"version"`
	Definition  string         `gorm:"type:json;not null" json:"definition"`
	Variables   string         `gorm:"type:json" json:"variables"`
	Triggers    string         `gorm:"type:json" json:"triggers"`
	Status      int8           `gorm:"default:0" json:"status"`
	IsTemplate  bool           `gorm:"default:false" json:"is_template"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Executions []WorkflowExecution `gorm:"foreignKey:WorkflowID" json:"executions,omitempty"`
}

// WorkflowExecution represents a workflow execution instance
type WorkflowExecution struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_workflow" json:"tenant_id"`
	WorkflowID  uint64         `gorm:"not null;index:idx_tenant_workflow" json:"workflow_id"`
	ExecutionID string         `gorm:"size:100;not null;uniqueIndex" json:"execution_id"`
	Status      string         `gorm:"size:20;not null" json:"status"`
	Input       string         `gorm:"type:json" json:"input"`
	Output      string         `gorm:"type:json" json:"output"`
	Context     string         `gorm:"type:json" json:"context"`
	StartedAt   *time.Time     `json:"started_at"`
	CompletedAt *time.Time     `json:"completed_at"`
	Duration    int64          `gorm:"default:0" json:"duration"` // milliseconds
	ErrorMessage string        `gorm:"type:text" json:"error_message"`
	TriggeredBy uint64         `json:"triggered_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Tasks []WorkflowTask `gorm:"foreignKey:ExecutionID;references:ExecutionID" json:"tasks,omitempty"`
}

// WorkflowTask represents a task within a workflow execution
type WorkflowTask struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_execution" json:"tenant_id"`
	ExecutionID string         `gorm:"size:100;not null;index:idx_tenant_execution" json:"execution_id"`
	TaskID      string         `gorm:"size:100;not null" json:"task_id"`
	TaskName    string         `gorm:"size:100;not null" json:"task_name"`
	TaskType    string         `gorm:"size:50;not null" json:"task_type"`
	Status      string         `gorm:"size:20;not null" json:"status"`
	Input       string         `gorm:"type:json" json:"input"`
	Output      string         `gorm:"type:json" json:"output"`
	Config      string         `gorm:"type:json" json:"config"`
	StartedAt   *time.Time     `json:"started_at"`
	CompletedAt *time.Time     `json:"completed_at"`
	Duration    int64          `gorm:"default:0" json:"duration"` // milliseconds
	RetryCount  int            `gorm:"default:0" json:"retry_count"`
	MaxRetries  int            `gorm:"default:3" json:"max_retries"`
	ErrorMessage string        `gorm:"type:text" json:"error_message"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// WorkflowSchedule represents workflow scheduling configuration
type WorkflowSchedule struct {
	ID          uint64         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID    uint64         `gorm:"not null;index:idx_tenant_workflow" json:"tenant_id"`
	WorkflowID  uint64         `gorm:"not null;index:idx_tenant_workflow" json:"workflow_id"`
	ScheduleName string        `gorm:"size:100;not null" json:"schedule_name"`
	CronExpression string      `gorm:"size:100;not null" json:"cron_expression"`
	Timezone    string         `gorm:"size:50;default:UTC" json:"timezone"`
	Input       string         `gorm:"type:json" json:"input"`
	IsActive    bool           `gorm:"default:true" json:"is_active"`
	NextRunAt   *time.Time     `json:"next_run_at"`
	LastRunAt   *time.Time     `json:"last_run_at"`
	CreatedBy   uint64         `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// Workflow statuses
const (
	WorkflowStatusDraft     = 0
	WorkflowStatusActive    = 1
	WorkflowStatusInactive  = 2
	WorkflowStatusArchived  = 3
)

// Execution statuses
const (
	ExecutionStatusPending   = "pending"
	ExecutionStatusRunning   = "running"
	ExecutionStatusCompleted = "completed"
	ExecutionStatusFailed    = "failed"
	ExecutionStatusCancelled = "cancelled"
	ExecutionStatusTimeout   = "timeout"
)

// Task statuses
const (
	TaskStatusPending   = "pending"
	TaskStatusRunning   = "running"
	TaskStatusCompleted = "completed"
	TaskStatusFailed    = "failed"
	TaskStatusSkipped   = "skipped"
	TaskStatusRetrying  = "retrying"
)

// Task types
const (
	TaskTypeHTTP         = "http"
	TaskTypeEmail        = "email"
	TaskTypeSMS          = "sms"
	TaskTypeDatabase     = "database"
	TaskTypeScript       = "script"
	TaskTypeCondition    = "condition"
	TaskTypeLoop         = "loop"
	TaskTypeParallel     = "parallel"
	TaskTypeDelay        = "delay"
	TaskTypeApproval     = "approval"
	TaskTypeNotification = "notification"
)

// Workflow categories
const (
	CategoryCommunication = "communication"
	CategoryMarketing     = "marketing"
	CategoryCustomer      = "customer"
	CategorySystem        = "system"
	CategoryIntegration   = "integration"
	CategoryApproval      = "approval"
)

// TableName returns the table name for Workflow
func (Workflow) TableName() string {
	return "workflows"
}

// TableName returns the table name for WorkflowExecution
func (WorkflowExecution) TableName() string {
	return "workflow_executions"
}

// TableName returns the table name for WorkflowTask
func (WorkflowTask) TableName() string {
	return "workflow_tasks"
}

// TableName returns the table name for WorkflowSchedule
func (WorkflowSchedule) TableName() string {
	return "workflow_schedules"
}

// IsActive checks if workflow is active
func (w *Workflow) IsActive() bool {
	return w.Status == WorkflowStatusActive
}

// IsDraft checks if workflow is draft
func (w *Workflow) IsDraft() bool {
	return w.Status == WorkflowStatusDraft
}

// IsCompleted checks if execution is completed
func (e *WorkflowExecution) IsCompleted() bool {
	return e.Status == ExecutionStatusCompleted
}

// IsFailed checks if execution failed
func (e *WorkflowExecution) IsFailed() bool {
	return e.Status == ExecutionStatusFailed
}

// IsRunning checks if execution is running
func (e *WorkflowExecution) IsRunning() bool {
	return e.Status == ExecutionStatusRunning
}

// GetDurationSeconds returns execution duration in seconds
func (e *WorkflowExecution) GetDurationSeconds() float64 {
	return float64(e.Duration) / 1000.0
}

// IsCompleted checks if task is completed
func (t *WorkflowTask) IsCompleted() bool {
	return t.Status == TaskStatusCompleted
}

// IsFailed checks if task failed
func (t *WorkflowTask) IsFailed() bool {
	return t.Status == TaskStatusFailed
}

// CanRetry checks if task can be retried
func (t *WorkflowTask) CanRetry() bool {
	return t.IsFailed() && t.RetryCount < t.MaxRetries
}

// IsActive checks if schedule is active
func (s *WorkflowSchedule) IsActive() bool {
	return s.IsActive
}

// ValidateTaskType validates if task type is supported
func ValidateTaskType(taskType string) bool {
	supportedTypes := []string{
		TaskTypeHTTP,
		TaskTypeEmail,
		TaskTypeSMS,
		TaskTypeDatabase,
		TaskTypeScript,
		TaskTypeCondition,
		TaskTypeLoop,
		TaskTypeParallel,
		TaskTypeDelay,
		TaskTypeApproval,
		TaskTypeNotification,
	}
	
	for _, supported := range supportedTypes {
		if taskType == supported {
			return true
		}
	}
	
	return false
}

// ValidateCategory validates if category is supported
func ValidateCategory(category string) bool {
	supportedCategories := []string{
		CategoryCommunication,
		CategoryMarketing,
		CategoryCustomer,
		CategorySystem,
		CategoryIntegration,
		CategoryApproval,
	}
	
	for _, supported := range supportedCategories {
		if category == supported {
			return true
		}
	}
	
	return false
}
